import React, { useState, useEffect, useRef } from 'react';
import { Mic, Mic<PERSON><PERSON>, Settings, Users, MessageSquare, Lock, Plus, Crown, Volume2, VolumeX, Gift, Smile, Send, X, Gamepad2, ChevronDown } from 'lucide-react';
import { wsService } from '../services/websocket';

interface User {
  id: string;
  name: string;
  avatar: string;
  isOwner?: boolean;
  isMuted?: boolean;
  isDeafened?: boolean;
  level?: number;
  isMutedByAdmin?: boolean;
  isChatMuted?: boolean; // منع من الكتابة
  isOnMic?: boolean; // على المايك أم في المحادثة فقط
  seatIndex?: number; // رقم المقعد
}

interface Room {
  _id?: string;
  id?: string;
  name: string;
  isLocked: boolean;
  password?: string;
  owner: User;
  participants: User[];
}

interface ChatMessage {
  id: string;
  user: User;
  message: string;
  timestamp: Date;
}

interface MicEmoji {
  id: string;
  emoji: string;
  x: number;
  y: number;
  timestamp: number;
}

interface SimpleVoiceRoomsProps {
  userData: any;
  onBack: () => void;
}

const SimpleVoiceRooms: React.FC<SimpleVoiceRoomsProps> = ({ userData, onBack }) => {
  const [currentUser, setCurrentUser] = useState<User>({
    id: userData?.id || '1',
    name: userData?.username || 'انت',
    avatar: userData?.profileImage || '👤',
    isOwner: false,
    isMuted: true, // مكتوم افتراضياً حتى يضغط على مقعد
    isDeafened: false,
    isChatMuted: false,
    isOnMic: false, // في المحادثة النصية فقط
    level: userData?.level || 14
  });

  const [rooms, setRooms] = useState<Room[]>([]);
  const [currentRoom, setCurrentRoom] = useState<Room | null>(null);
  const [seats, setSeats] = useState<(User | null)[]>([
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null
  ]);
  const [loading, setLoading] = useState(true);

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      user: { id: 'system', name: 'النظام', avatar: '⚙' },
      message: 'مرحبا بكم في الغرفة الصوتية',
      timestamp: new Date()
    }
  ]);

  const [newMessage, setNewMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [micEmojis, setMicEmojis] = useState<MicEmoji[]>([]);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [newRoomName, setNewRoomName] = useState('');
  const [newRoomPassword, setNewRoomPassword] = useState('');
  const [isNewRoomLocked, setIsNewRoomLocked] = useState(false);

  // إعدادات تخصيص اسم الغرفة
  const [showCustomizeRoom, setShowCustomizeRoom] = useState(false);
  const [customRoomName, setCustomRoomName] = useState('');
  const [showGameShortcuts, setShowGameShortcuts] = useState(false);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isMicEnabled, setIsMicEnabled] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [showAdminMenu, setShowAdminMenu] = useState<string | null>(null); // معرف المستخدم المحدد للإدارة
  const [showRoomAdminPanel, setShowRoomAdminPanel] = useState(false); // لوحة إدارة الغرف

  // Ref for chat container to handle auto-scroll
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const chatEmojis = ['😀', '😂', '😍', '😎', '🤔', '😢', '😡', '👍', '👎', '❤', '🔥', '⭐', '🎉'];
  const avatarEmojis = ['👨', '👩', '👤', '🧑', '👦', '👧'];
  const micEmojiList = ['😀', '😂', '😍', '😎', '👍', '👎', '❤', '🔥', '⭐', '🎉', '👏', '💪'];

  // جلب الغرف من السيرفر
  useEffect(() => {
    const fetchRooms = async () => {
      try {
        const response = await fetch('/api/voice-rooms');
        if (response.ok) {
          const roomsData = await response.json();
          setRooms(roomsData);
        }
      } catch (error) {
        console.error('خطأ في جلب الغرف:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();

    // الاتصال بـ WebSocket
    wsService.connect().then(() => {
      console.log('✅ WebSocket connected successfully');
    }).catch(error => {
      console.error('❌ WebSocket connection failed:', error);
    });

    // استقبال تحديثات WebSocket وتحديث الغرف تلقائيًا
    function handleRoomUpdate(message: any) {
      console.log('🔄 Room update received:', message);
      if ([
        'player_joined',
        'player_left',
        'room_created',
        'room_deleted',
        'rooms_updated'
      ].includes(message.type) ||
      (message.type === 'voice_room_update' && message.updateType === 'rooms_updated')) {
        console.log('📋 Refreshing rooms list...');
        fetchRooms();
      }
    }

    // دالة لجلب حالة الغرفة مباشرة من السيرفر
    const fetchRoomState = async () => {
      if (!currentRoom) return;

      try {
        console.log('🔄 Fetching fresh room state from server...');
        const response = await fetch(`/api/voice-rooms/${currentRoom._id}`);
        if (response.ok) {
          const roomData = await response.json();
          console.log('📊 Fresh room data from server:', roomData);

          // تحديث حالة الغرفة مباشرة من السيرفر
          setCurrentRoom(roomData);

          // تحديث المقاعد مباشرة من بيانات السيرفر
          const newSeats = Array(10).fill(null);
          if (roomData.participants) {
            roomData.participants.forEach((participant: any) => {
              if (participant.seatIndex !== undefined && participant.seatIndex !== null) {
                const seatIndex = participant.seatIndex - 1;
                if (seatIndex >= 0 && seatIndex < 10) {
                  newSeats[seatIndex] = {
                    id: participant.userId,
                    name: participant.username,
                    avatar: participant.avatar,
                    isOnMic: true,
                    isMuted: participant.isMuted || false,
                    isDeafened: false,
                    seatIndex: participant.seatIndex,
                    isOwner: false
                  };
                }
              }
            });
          }
          console.log('🪑 Updated seats from server:', newSeats);
          setSeats(newSeats);

          // تحديث قائمة الرسائل من السيرفر إذا كانت متوفرة
          if (roomData.messages) {
            setChatMessages(roomData.messages.map((msg: any) => ({
              id: msg._id || Date.now().toString(),
              user: msg.user,
              message: msg.message,
              timestamp: new Date(msg.timestamp)
            })));
          }
        }
      } catch (error) {
        console.error('❌ Error fetching room state:', error);
      }
    };

    // استقبال تحديثات الغرف الصوتية
    function handleVoiceRoomUpdate(message: any) {
      console.log('🔄 Voice room update received:', message);
      console.log('📨 Message room ID:', message.roomId);
      console.log('🏠 Current room ID:', currentRoom?._id);

      // تحديث قائمة الغرف إذا كان التحديث عام
      if (message.updateType === 'rooms_updated' || message.roomId === 'all') {
        console.log('📋 Updating rooms list due to general update');
        fetchRooms();
        return;
      }

      // تحقق من مطابقة room ID للغرفة الحالية
      const roomMatches = currentRoom && (
        message.roomId === currentRoom._id ||
        message.roomId === currentRoom.id
      );

      console.log('🔍 Room matches:', roomMatches);

      if (message.type === 'voice_room_update' && roomMatches) {
        console.log('✅ Room update received, fetching fresh state from server');
        // بدلاً من تحديث الحالة محلياً، نجلب الحالة الجديدة من السيرفر
        fetchRoomState();

        // تحديث قائمة الغرف أيضاً لإظهار عدد المشاركين المحدث
        fetchRooms();
      } else if (!roomMatches && message.type === 'voice_room_update') {
        console.log('📋 Update for different room, refreshing rooms list');
        // تحديث قائمة الغرف حتى لو لم تكن في الغرفة المحدثة
        fetchRooms();
      } else {
        console.log('❌ Update ignored - room mismatch or no current room');
      }
    }

    // استقبال رسائل الغرف الصوتية
    function handleVoiceRoomMessage(message: any) {
      console.log('💬 Voice room message received:', message);
      console.log('💬 Current room when message received:', currentRoom);
      console.log('💬 Message room ID:', message.roomId);
      console.log('💬 Current room _id:', currentRoom?._id);

      // حفظ آخر رسالة للاستخدام في العمليات اللاحقة
      (window as any).lastVoiceRoomMessage = message;

      const roomMatches = currentRoom && (
        message.roomId === currentRoom._id ||
        message.roomId === currentRoom.id ||
        message.roomId === (currentRoom._id || currentRoom.id)
      );

      if (message.type === 'voice_room_message' && roomMatches) {
        const newMessage: ChatMessage = {
          id: Date.now().toString() + Math.random(),
          user: message.messageData.user,
          message: message.messageData.message,
          timestamp: new Date(message.messageData.timestamp)
        };

        setChatMessages(prev => [...prev, newMessage]);
      }
    }

    console.log('🔧 Setting up WebSocket listeners...');
    console.log('🔧 Current room when setting up listeners:', currentRoom);

    wsService.on('player_joined', handleRoomUpdate);
    wsService.on('player_left', handleRoomUpdate);
    wsService.on('room_created', handleRoomUpdate);
    wsService.on('room_deleted', handleRoomUpdate);
    wsService.on('voice_room_update', handleVoiceRoomUpdate);
    wsService.on('voice_room_message', handleVoiceRoomMessage);

    console.log('✅ WebSocket listeners set up successfully');

    return () => {
      wsService.off('player_joined', handleRoomUpdate);
      wsService.off('player_left', handleRoomUpdate);
      wsService.off('room_created', handleRoomUpdate);
      wsService.off('room_deleted', handleRoomUpdate);
      wsService.off('voice_room_update', handleVoiceRoomUpdate);
      wsService.off('voice_room_message', handleVoiceRoomMessage);
    };
  }, []);

  // جلب حالة الغرفة من السيرفر عند دخولها
  useEffect(() => {
    if (!currentRoom) return;

    const fetchRoomState = async () => {
      try {
        console.log('🔄 Fetching initial room state from server...');
        const response = await fetch(`/api/voice-rooms/${currentRoom._id}`);
        if (response.ok) {
          const roomData = await response.json();
          console.log('📊 Initial room data from server:', roomData);

          // تحديث حالة الغرفة مباشرة من السيرفر
          setCurrentRoom(roomData);

          // تحديث المقاعد مباشرة من بيانات السيرفر
          const newSeats = Array(10).fill(null);
          if (roomData.participants) {
            roomData.participants.forEach((participant: any) => {
              if (participant.seatIndex !== undefined && participant.seatIndex !== null) {
                const seatIndex = participant.seatIndex - 1;
                if (seatIndex >= 0 && seatIndex < 10) {
                  newSeats[seatIndex] = {
                    id: participant.userId,
                    name: participant.username,
                    avatar: participant.avatar,
                    isOnMic: true,
                    isMuted: participant.isMuted || false,
                    isDeafened: false,
                    seatIndex: participant.seatIndex,
                    isOwner: false
                  };
                }
              }
            });
          }
          console.log('🪑 Initial seats from server:', newSeats);
          setSeats(newSeats);
        }
      } catch (error) {
        console.error('❌ Error fetching initial room state:', error);
      }
    };

    fetchRoomState();

    // تحديث دوري كل 5 ثوانٍ لضمان التزامن مع السيرفر
    const interval = setInterval(() => {
      console.log('🔄 Periodic room state refresh...');
      fetchRoomState();
    }, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [currentRoom?._id]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // إغلاق قائمة الإدارة عند الضغط خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAdminMenu) {
        setShowAdminMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAdminMenu]);

  // وظائف إدارة الغرفة للمالك
  const kickUserFromMic = (userId: string) => {
    if (currentUser.isOwner && userId !== currentUser.id) {
      setSeats(prev => prev.map(user =>
        user && user.id === userId
          ? { ...user, isOnMic: false, isMuted: true, seatIndex: undefined }
          : user
      ));
    }
  };

  const muteUserChat = (userId: string) => {
    if (currentUser.isOwner && userId !== currentUser.id) {
      setSeats(prev => prev.map(user =>
        user && user.id === userId
          ? { ...user, isChatMuted: !user.isChatMuted }
          : user
      ));
    }
  };

  const muteUserMic = (userId: string) => {
    if (currentUser.isOwner && userId !== currentUser.id) {
      setSeats(prev => prev.map(user =>
        user && user.id === userId
          ? { ...user, isMuted: !user.isMuted }
          : user
      ));
    }
  };

  // الضغط على مقعد للانضمام للمايك
  const joinMicSeat = async (seatIndex: number) => {
    if (!currentRoom || !(currentRoom._id || currentRoom.id)) {
      alert('لم يتم تحميل بيانات الغرفة بعد. الرجاء الانتظار حتى تظهر بيانات الغرفة بالكامل ثم حاول مرة أخرى.');
      return;
    }
    console.log('🎤 Attempting to join mic seat:', seatIndex);
    console.log('🪑 Current seat state:', seats[seatIndex]);
    console.log('🎙️ User on mic:', currentUser.isOnMic);

    // استخدام الغرفة المُمررة أو الغرفة الحالية أو البيانات المحفوظة
    const targetRoom = currentRoom || (window as any).currentRoomData;
    console.log('🏠 Target room:', targetRoom?._id || targetRoom?.id);

    // التحقق من وجود الغرفة الحالية
    if (!targetRoom) {
      console.error('❌ No room available for joining mic');
      return;
    }

    if (seats[seatIndex] === null && !currentUser.isOnMic) {
      try {
        const token = localStorage.getItem('token');
        console.log('📡 Sending join-mic request...');
        console.log('🏠 Target room object:', targetRoom);
        console.log('🆔 Room ID for request:', targetRoom?._id || targetRoom?.id);

        // استخدام roomId من مصادر متعددة
        let roomId = targetRoom?._id || targetRoom?.id || (window as any).currentRoomId;

        // إذا لم نجد roomId، نحاول استخراجه من آخر رسالة WebSocket
        if (!roomId && (window as any).lastVoiceRoomMessage) {
          roomId = (window as any).lastVoiceRoomMessage.roomId;
          console.log('🔄 Using roomId from last WebSocket message:', roomId);
        }

        // حل مؤقت: استخدام roomId ثابت للاختبار
        if (!roomId) {
          roomId = '686b51808dd30bd82ba87168'; // غرفة ASD
          console.log('🔧 Using fallback roomId for testing:', roomId);
        }

        console.log('🆔 Final room ID:', roomId);

        if (!roomId) {
          console.error('❌ No room ID available from any source');
          return;
        }

        const response = await fetch(`/api/voice-rooms/${roomId}/join-mic`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ seatIndex })
        });

        console.log('📡 Join-mic response status:', response.status);

        if (response.ok) {
          const result = await response.json();

          const updatedUser = {
            ...currentUser,
            isOnMic: true,
            isMuted: false,
            seatIndex: seatIndex
          };
          setCurrentUser(updatedUser);

          // إزالة المستخدم من أي مقعد آخر أولاً
          const newSeats = seats.map(user =>
            user && user.id === currentUser.id ? null : user
          );
          // ثم إضافته للمقعد الجديد
          newSeats[seatIndex] = updatedUser;
          setSeats(newSeats);

          // رسالة انضمام للمايك
          const joinMessage: ChatMessage = {
            id: Date.now().toString(),
            user: { id: 'system', name: 'النظام', avatar: '🎤' },
            message: `${currentUser.name} انضم للمايك`,
            timestamp: new Date()
          };
          setChatMessages(prev => [...prev, joinMessage]);
        } else {
          const error = await response.json();
          if (error.error === 'أنت على المايك بالفعل') {
            // إذا كان المستخدم على المايك بالفعل، اعرض خيار المغادرة
            const confirmLeave = confirm('أنت على المايك بالفعل. هل تريد مغادرة المايك؟');
            if (confirmLeave) {
              await leaveMic();
            }
          } else {
            alert(error.error || 'خطأ في الانضمام للمايك');
          }
        }
      } catch (error) {
        console.error('خطأ في الانضمام للمايك:', error);
        alert('خطأ في الاتصال بالسيرفر');
      }
    }
  };

  // مغادرة المايك
  const leaveMic = async () => {
    if (!currentRoom || !currentUser.isOnMic) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/voice-rooms/${currentRoom._id || currentRoom.id}/leave-mic`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const updatedUser = {
          ...currentUser,
          isOnMic: false,
          isMuted: true,
          seatIndex: undefined
        };
        setCurrentUser(updatedUser);

        const newSeats = seats.map(user =>
          user && user.id === currentUser.id ? null : user
        );
        setSeats(newSeats);

        // رسالة مغادرة المايك
        const leaveMessage: ChatMessage = {
          id: Date.now().toString(),
          user: { id: 'system', name: 'النظام', avatar: '💬' },
          message: `${currentUser.name} عاد للمحادثة النصية`,
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, leaveMessage]);
      } else {
        const error = await response.json();
        alert(error.error || 'خطأ في مغادرة المايك');
      }
    } catch (error) {
      console.error('خطأ في مغادرة المايك:', error);
      alert('خطأ في الاتصال بالسيرفر');
    }
  };

  // مغادرة المايك والعودة للمحادثة النصية
  const leaveMicSeat = async () => {
    if (currentUser.isOnMic && currentRoom) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/voice-rooms/${currentRoom._id || currentRoom.id}/leave-mic`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const updatedUser = {
            ...currentUser,
            isOnMic: false,
            isMuted: true,
            seatIndex: undefined
          };
          setCurrentUser(updatedUser);

          const newSeats = seats.map(user =>
            user && user.id === currentUser.id ? null : user
          );
          setSeats(newSeats);

          // رسالة مغادرة المايك
          const leaveMessage: ChatMessage = {
            id: Date.now().toString(),
            user: { id: 'system', name: 'النظام', avatar: '💬' },
            message: `${currentUser.name} عاد للمحادثة النصية`,
            timestamp: new Date()
          };
          setChatMessages(prev => [...prev, leaveMessage]);
        } else {
          const error = await response.json();
          alert(error.error || 'خطأ في مغادرة المايك');
        }
      } catch (error) {
        console.error('خطأ في مغادرة المايك:', error);
        alert('خطأ في الاتصال بالسيرفر');
      }
    }
  };

  const toggleMute = (userId: string) => {
    if (userId === currentUser.id && currentUser.isOnMic) {
      const updatedUser = { ...currentUser, isMuted: !currentUser.isMuted };
      setCurrentUser(updatedUser);

      setSeats(prev => prev.map(user =>
        user && user.id === userId ? updatedUser : user
      ));
    }
  };

  const toggleDeafen = () => {
    setSeats(prev => prev.map(user =>
      user && user.id === currentUser.id
        ? { ...user, isDeafened: !user.isDeafened }
        : user
    ));
  };

  // حذف غرفة شخصية معينة (للإدارة فقط)
  const deleteRoom = async (roomId: string, roomName: string) => {
    if (!userData?.isAdmin) {
      alert('غير مصرح لك بحذف الغرف');
      return;
    }

    const confirmDelete = confirm(`هل أنت متأكد من حذف الغرفة "${roomName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmDelete) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/voice-rooms/${roomId}/admin-delete`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message);

        // إزالة الغرفة من القائمة
        setRooms(prev => prev.filter(room => (room._id || room.id) !== roomId));

        // إذا كان المستخدم في هذه الغرفة، إخراجه
        if (currentRoom && (currentRoom._id || currentRoom.id) === roomId) {
          setCurrentRoom(null);
          setSeats(Array(10).fill(null));
          setChatMessages([]);
        }
      } else {
        const error = await response.json();
        alert(error.error || 'خطأ في حذف الغرفة');
      }
    } catch (error) {
      console.error('خطأ في حذف الغرفة:', error);
      alert('خطأ في الاتصال بالسيرفر');
    }
  };

  // حذف جميع الغرف الشخصية (للإدارة فقط)
  const deleteAllRooms = async () => {
    if (!userData?.isAdmin) {
      alert('غير مصرح لك بحذف الغرف');
      return;
    }

    const confirmDelete = confirm(`هل أنت متأكد من حذف جميع الغرف؟\nسيتم حذف ${rooms.length} غرفة\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmDelete) return;

    const doubleConfirm = confirm('تأكيد نهائي: حذف جميع الغرف؟');
    if (!doubleConfirm) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/voice-rooms/admin-delete-all', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message);

        // مسح جميع الغرف من القائمة
        setRooms([]);
        setCurrentRoom(null);
        setSeats(Array(10).fill(null));
        setChatMessages([]);
      } else {
        const error = await response.json();
        alert(error.error || 'خطأ في حذف الغرف');
      }
    } catch (error) {
      console.error('خطأ في حذف جميع الغرف:', error);
      alert('خطأ في الاتصال بالسيرفر');
    }
  };

  // دالة تحديث اسم الغرفة الشخصية
  const updateRoomName = async () => {
    if (!customRoomName.trim()) {
      alert('يرجى إدخال اسم الغرفة');
      return;
    }

    if (customRoomName.trim().length > 50) {
      alert('اسم الغرفة يجب أن يكون أقل من 50 حرف');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/voice-rooms/update-name', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newName: customRoomName.trim() })
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message);

        // تحديث قائمة الغرف
        await fetchRooms();

        // إغلاق نافذة التخصيص
        setShowCustomizeRoom(false);
        setCustomRoomName('');
      } else {
        const error = await response.json();
        alert(error.error || 'خطأ في تحديث اسم الغرفة');
      }
    } catch (error) {
      console.error('خطأ في تحديث اسم الغرفة:', error);
      alert('خطأ في الاتصال بالخادم');
    }
  };

  const addMicEmoji = (emoji: string) => {
    const newEmoji: MicEmoji = {
      id: Date.now().toString(),
      emoji,
      x: Math.random() * 200 - 100,
      y: 0,
      timestamp: Date.now()
    };

    setMicEmojis(prev => [...prev, newEmoji]);

    setTimeout(() => {
      requestAnimationFrame(() => {
        setMicEmojis(prev => prev.filter(e => e.id !== newEmoji.id));
      });
    }, 3000);
  };

  const sendMessage = () => {
    if (newMessage.trim() && currentRoom && !currentUser.isMuted) {
      const message: ChatMessage = {
        id: Date.now().toString(),
        user: currentUser,
        message: newMessage,
        timestamp: new Date()
      };

      // إضافة الرسالة محلياً
      setChatMessages([...chatMessages, message]);

      // إرسال الرسالة عبر WebSocket لجميع المستخدمين في الغرفة
      wsService.send({
        type: 'voice_room_message',
        data: {
          roomId: currentRoom._id,
          messageData: message
        }
      });

      setNewMessage('');
    }
  };

  const addEmojiToMessage = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  const toggleSpeaker = () => {
    setIsAudioEnabled(!isAudioEnabled);
  };

  const toggleMicrophone = () => {
    setIsConnecting(true);
    setTimeout(() => {
      setIsMicEnabled(!isMicEnabled);
      setIsConnecting(false);
    }, 1000);
  };

  const gameShortcuts = [
    { id: 1, name: 'Fruit Catch', icon: '🍎', url: '/fruit-catching' },
    { id: 2, name: 'Lucky Boxes', icon: '📦', url: '/box-game' },
    { id: 3, name: 'Forest Game', icon: '🌲', url: '/forest-game' },
    { id: 4, name: 'Mind Puzzle', icon: '🧩', url: '/mind-puzzle' },
    { id: 5, name: 'Coming Soon', icon: '🎯', url: '#' }
  ];

  const createRoom = async () => {
    if (newRoomName.trim()) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/voice-rooms', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            name: newRoomName.trim(),
            isLocked: isNewRoomLocked,
            password: isNewRoomLocked ? newRoomPassword : undefined
          })
        });

        if (response.ok) {
          const newRoom = await response.json();
          setRooms(prev => [...prev, newRoom]);
          setCurrentRoom(newRoom);

          // المالك يصبح مدير الغرفة ويجلس في المقعد الأول
          const ownerUser = {
            ...currentUser,
            isOwner: true,
            isOnMic: true,
            isMuted: false,
            seatIndex: 0
          };
          setCurrentUser(ownerUser);
          setSeats([ownerUser, ...Array(9).fill(null)]);

          setNewRoomName('');
          setNewRoomPassword('');
          setIsNewRoomLocked(false);
          setShowCreateRoom(false);

          // رسائل ترحيبية متعددة
          const welcomeMessages: ChatMessage[] = [
            {
              id: Date.now().toString(),
              user: { id: 'system', name: 'النظام', avatar: '⚙' },
              message: `تم انشاء الغرفة "${newRoom.name}" بنجاح`,
              timestamp: new Date()
            },
            {
              id: (Date.now() + 1).toString(),
              user: { id: 'system', name: 'النظام', avatar: '👑' },
              message: `${currentUser.name} أصبح مدير الغرفة`,
              timestamp: new Date()
            }
          ];
          setChatMessages(welcomeMessages);
        } else {
          const error = await response.json();
          alert(error.error || 'خطأ في إنشاء الغرفة');
        }
      } catch (error) {
        console.error('خطأ في إنشاء الغرفة:', error);
        alert('خطأ في الاتصال بالسيرفر');
      }
    }
  };

  const joinRoom = async (room: Room) => {
    try {
      console.log('🚪 Attempting to join room:', room.name, room._id || room.id);
      let password = '';
      if (room.isLocked) {
        password = prompt('ادخل كلمة المرور:') || '';
        if (!password) return;
      }

      const token = localStorage.getItem('token');
      const roomId = room._id || room.id;
      console.log('🔑 Using token:', token ? 'Present' : 'Missing');
      console.log('🏠 Room ID:', roomId);

      // حفظ roomId للاستخدام في العمليات اللاحقة
      (window as any).currentRoomId = roomId;

      const response = await fetch(`/api/voice-rooms/${roomId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ password })
      });

      console.log('📡 Join response status:', response.status);

      if (response.ok) {
        const updatedRoom = await response.json();
        console.log('✅ Successfully joined room:', updatedRoom);
        console.log('🏠 Setting current room to:', updatedRoom._id || updatedRoom.id);
        setCurrentRoom(updatedRoom);

        // إضافة المشاركين الموجودين على المايك فقط للمقاعد
        const micParticipants: User[] = [];
        let currentUserOnMic = false;
        let currentUserSeatIndex: number | undefined = undefined;

        if (updatedRoom.participants) {
          updatedRoom.participants.forEach((participant: any) => {
            const participantUser = {
              id: participant.userId,
              name: participant.username,
              avatar: participant.avatar || '👤',
              level: participant.level || 1,
              isOnMic: true,
              isMuted: participant.isMuted || false,
              seatIndex: participant.seatIndex
            };

            micParticipants.push(participantUser);

            // التحقق إذا كان المستخدم الحالي موجود في المشاركين
            if (participant.userId === currentUser.id) {
              currentUserOnMic = true;
              currentUserSeatIndex = participant.seatIndex;
            }
          });
        }

        // تحديث حالة المستخدم الحالي إذا كان على المايك
        if (currentUserOnMic) {
          setCurrentUser(prev => ({
            ...prev,
            isOnMic: true,
            seatIndex: currentUserSeatIndex
          }));
        }

        // ملء المقاعد
        const newSeats = Array(10).fill(null);
        micParticipants.forEach(participant => {
          if (participant.seatIndex !== undefined && participant.seatIndex < 10) {
            newSeats[participant.seatIndex] = participant;
          }
        });
        setSeats(newSeats);

        // إرسال معلومات الانضمام عبر WebSocket للمزامنة
        wsService.send({
          type: 'join_voice_room',
          data: {
            userId: currentUser.id,
            roomId: room._id,
            userInfo: {
              name: currentUser.name,
              avatar: currentUser.avatar
            }
          }
        });

        // انتظار قصير للتأكد من تحديث الحالة قبل السماح بالانضمام للمايك
        setTimeout(() => {
          console.log('🔄 Room state updated, ready for mic operations');
          console.log('🏠 Updated room available for mic operations:', updatedRoom._id || updatedRoom.id);
        }, 100);

        // حفظ الغرفة في متغير محلي للاستخدام الفوري
        window.currentRoomData = updatedRoom;

        // رسالة ترحيبية شخصية (لا نجلب رسائل محفوظة)
        const welcomeMessage: ChatMessage = {
          id: Date.now().toString(),
          user: { id: 'system', name: 'النظام', avatar: '🎉' },
          message: `مرحباً ${currentUser.name}! أهلاً بك في "${room.name}"`,
          timestamp: new Date()
        };
        setChatMessages([welcomeMessage]);
      } else {
        const error = await response.json();
        alert(error.error || 'خطأ في الانضمام للغرفة');
      }
    } catch (error) {
      console.error('خطأ في الانضمام للغرفة:', error);
      alert('خطأ في الاتصال بالسيرفر');
    }
  };

  const leaveRoom = async () => {
    if (!currentRoom) return;

    try {
      const token = localStorage.getItem('token');
      const roomId = currentRoom._id || currentRoom.id;
      await fetch(`/api/voice-rooms/${roomId}/leave`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // إرسال معلومات المغادرة عبر WebSocket للمزامنة
      wsService.send({
        type: 'leave_voice_room',
        data: {
          userId: currentUser.id,
          roomId: roomId
        }
      });
    } catch (error) {
      console.error('خطأ في مغادرة الغرفة:', error);
    }

    setCurrentRoom(null);
    setSeats(Array(10).fill(null));
    // لا نحذف الرسائل - تبقى محفوظة
    // setChatMessages([]);
  };

  useEffect(() => {
    const handleBeforeUnload = () => {
      if (currentRoom) {
        wsService.send({
          type: 'leave_voice_room',
          data: {
            userId: currentUser.id,
            roomId: currentRoom._id || currentRoom.id
          }
        });
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // عند إزالة المكون (unmount) أيضًا
      handleBeforeUnload();
    };
  }, [currentRoom, currentUser.id]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-900 text-white">
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>

      {/* Header */}
      <div className="bg-black/30 backdrop-blur-md border-b border-white/10 sticky top-0 z-40">
        <div className="px-3 py-2 sm:px-4 sm:py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button
                onClick={onBack}
                className="bg-gray-500 hover:bg-gray-600 p-2 rounded-lg transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-1 sm:space-x-2">
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full px-2 py-1 flex items-center space-x-1">
                  <span className="text-black font-bold text-xs sm:text-sm">{userData?.goldCoins || 0}</span>
                  <Gift className="h-3 w-3 sm:h-4 sm:w-4 text-black" />
                </div>
                <div className="bg-gradient-to-r from-blue-400 to-purple-500 rounded-full px-2 py-1 flex items-center space-x-1">
                  <span className="text-white font-bold text-xs sm:text-sm">{userData?.pearls || 0}</span>
                  <span className="text-white text-xs sm:text-sm">💎</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="text-right">
                <div className="text-sm sm:text-base font-bold">{currentUser.name}</div>
                <div className="text-xs text-white opacity-70">المستوى {currentUser.level}</div>
              </div>
              <div className="flex-shrink-0">
                {currentUser.avatar && currentUser.avatar.startsWith('data:') ? (
                  <img
                    src={currentUser.avatar}
                    alt={currentUser.name}
                    className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="text-2xl sm:text-3xl">{currentUser.avatar || '👤'}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-2 py-4 sm:px-4 sm:py-8">
        {!currentRoom ? (
          /* Room List View */
          <div className="max-w-4xl mx-auto">
            <div className="bg-black/20 backdrop-blur-md rounded-xl p-4 sm:p-8 shadow-xl border border-white/10">
              <div className="text-center mb-6 sm:mb-8">
                <h2 className="text-2xl sm:text-3xl font-bold text-yellow-400 mb-2">الغرف الصوتية الشخصية</h2>
                <p className="text-white/60 text-sm">كل لاعب له غرفة صوتية شخصية يمكن تخصيص اسمها</p>

                {/* أزرار المستخدم وإدارة الأدمن */}
                <div className="mt-4 flex flex-wrap justify-center gap-2">
                  {/* زر تخصيص اسم الغرفة للمستخدم */}
                  <button
                    onClick={() => setShowCustomizeRoom(true)}
                    className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm font-medium transition-colors flex items-center gap-2"
                  >
                    <Settings className="w-4 h-4" />
                    تخصيص اسم غرفتي
                  </button>

                  {/* أزرار إدارة الأدمن */}
                  {userData?.isAdmin && (
                    <button
                      onClick={() => deleteAllRooms()}
                      className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-white text-sm font-medium transition-colors flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                      حذف جميع الغرف
                    </button>
                  )}
                </div>
              </div>

              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin h-8 w-8 border-4 border-yellow-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-white/70">جاري تحميل الغرف...</p>
                </div>
              ) : rooms.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🎤</div>
                  <h3 className="text-xl font-semibold mb-2">لا توجد غرف نشطة حالياً</h3>
                  <p className="text-white/70 mb-4">لا يوجد أي لاعب في غرفته الصوتية الآن</p>
                  <p className="text-white/50 text-sm">ادخل إلى غرفتك الصوتية من البروفايل لتظهر هنا</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {rooms.map((room) => (
                    <div key={room._id || room.id} className="relative">
                      <button
                        onClick={() => joinRoom(room)}
                        className="w-full bg-gradient-to-br from-slate-800/60 to-purple-800/60 border border-purple-400/30 hover:from-slate-700/70 hover:to-purple-700/70 hover:border-purple-300/50 p-5 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg backdrop-blur-sm"
                      >
                      {/* Header with room name and owner avatar */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          {room.isLocked && <Lock className="h-4 w-4 text-yellow-400" />}
                          <div>
                            <h3 className="font-bold text-lg text-white truncate max-w-[150px]" title={room.name}>
                              {room.name}
                            </h3>
                            <p className="text-xs text-purple-200/70">غرفة شخصية</p>
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          {room.owner.avatar && room.owner.avatar.startsWith('data:') ? (
                            <img
                              src={room.owner.avatar}
                              alt={room.owner.name}
                              className="w-10 h-10 rounded-full object-cover border-2 border-purple-400/50"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center text-white text-lg border-2 border-purple-400/50">
                              {room.owner.avatar || '👤'}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Room info */}
                      <div className="text-right space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-purple-200/80">المالك:</span>
                          <span className="text-sm text-white font-medium">{room.owner.name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-purple-200/80">المشاركون:</span>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-blue-400" />
                            <span className={`text-sm font-bold ${
                              (room.participants?.length || 0) >= 9
                                ? 'text-red-400'
                                : (room.participants?.length || 0) >= 6
                                  ? 'text-yellow-400'
                                  : 'text-green-400'
                            }`}>
                              {room.participants?.length || 0}/10
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Join button indicator */}
                      <div className="mt-3 pt-3 border-t border-purple-400/20">
                        <div className="text-center">
                          <span className="text-xs text-purple-200/60">اضغط للانضمام</span>
                        </div>
                      </div>
                    </button>

                      {/* زر حذف الغرفة للأدمن */}
                      {userData?.isAdmin && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteRoom(room._id || room.id, room.name);
                          }}
                          className="absolute top-2 left-2 bg-red-600/80 hover:bg-red-700 p-1.5 rounded-lg transition-colors z-10"
                          title={`حذف غرفة ${room.name}`}
                        >
                          <X className="w-3 h-3 text-white" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          /* Room View */
          <div className="space-y-4 lg:grid lg:grid-cols-4 lg:gap-8 lg:space-y-0">
            {/* Room List Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-black/20 backdrop-blur-md rounded-xl p-3 sm:p-6 shadow-xl border border-white/10">
                <div className="flex items-center justify-between mb-3 sm:mb-4">
                  <h2 className="text-lg sm:text-xl font-bold">متصل</h2>
                  <button
                    onClick={leaveRoom}
                    className="bg-red-500 hover:bg-red-600 p-2 rounded-lg transition-colors"
                    title="مغادرة الغرفة"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>

                {/* تم إزالة لوحة إدارة الغرف من هنا - متاحة في الصفحة الرئيسية للإدارة العامة */}
                <div className="space-y-3">
                  {/* عرض المستخدمين المتواجدين في الغرفة */}
                  {seats.filter(user => user !== null).map((user, index) => (
                    <div
                      key={user?.id || index}
                      className="relative"
                    >
                      <div
                        className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                          currentUser.isOwner && user?.id !== currentUser.id
                            ? 'bg-black/30 border-white/20 hover:bg-black/50 hover:border-white/40'
                            : 'bg-black/30 border-white/20'
                        }`}
                        onClick={() => {
                          // صاحب الغرفة يمكنه إدارة المستخدمين الآخرين
                          if (currentUser.isOwner && user?.id !== currentUser.id) {
                            setShowAdminMenu(showAdminMenu === user.id ? null : user.id);
                          }
                          // المستخدم يمكنه التحكم في نفسه فقط (كتم المايك)
                          else if (user?.id === currentUser.id && currentUser.isOnMic) {
                            toggleMute(user.id);
                          }
                        }}
                      >
                        <div className="flex-shrink-0">
                          {user?.avatar && user.avatar.startsWith('data:') ? (
                            <img
                              src={user.avatar}
                              alt={user.name}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-lg">
                              {user?.avatar || '👤'}
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-sm truncate">{user?.name}</h3>
                            {user?.isOwner && <Crown className="h-3 w-3 text-yellow-400" />}
                            {currentUser.isOwner && user?.id !== currentUser.id && (
                              <span className="text-xs text-yellow-400">👆 إدارة</span>
                            )}
                            {user?.id === currentUser.id && currentUser.isOnMic && (
                              <span className="text-xs text-green-400">👆 كتم/إلغاء كتم</span>
                            )}
                          </div>
                          {user?.level && (
                            <p className="text-xs text-blue-400">المستوى {user.level}</p>
                          )}
                        </div>
                        <div className="flex items-center space-x-1">
                          {user?.isMuted ? (
                            <MicOff className="h-4 w-4 text-red-400" />
                          ) : (
                            <Mic className="h-4 w-4 text-green-400" />
                          )}
                          {user?.isDeafened && (
                            <VolumeX className="h-4 w-4 text-red-400" />
                          )}
                          {user?.isChatMuted && (
                            <MessageSquare className="h-4 w-4 text-red-400" />
                          )}
                        </div>
                      </div>

                      {/* قائمة إدارة مدير الغرفة */}
                      {showAdminMenu === user?.id && currentUser.isOwner && (
                        <div className="absolute top-full left-0 right-0 mt-1 bg-black/90 backdrop-blur-md rounded-lg border border-yellow-400/30 shadow-xl z-10 animate-fadeIn">
                          <div className="p-2 space-y-1">
                            <div className="text-xs text-yellow-300 mb-2 text-center">👑 إدارة مدير الغرفة</div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                kickUserFromMic(user.id);
                                setShowAdminMenu(null);
                              }}
                              className="w-full text-left p-2 rounded bg-red-500/20 hover:bg-red-500/40 text-red-300 text-sm transition-colors"
                            >
                              ⬇️ إنزال من المايك
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                muteUserMic(user.id);
                                setShowAdminMenu(null);
                              }}
                              className="w-full text-left p-2 rounded bg-orange-500/20 hover:bg-orange-500/40 text-orange-300 text-sm transition-colors"
                            >
                              🔇 {user.isMuted ? 'إلغاء كتم المايك' : 'كتم المايك'}
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                muteUserChat(user.id);
                                setShowAdminMenu(null);
                              }}
                              className="w-full text-left p-2 rounded bg-purple-500/20 hover:bg-purple-500/40 text-purple-300 text-sm transition-colors"
                            >
                              💬 {user.isChatMuted ? 'السماح بالكتابة' : 'منع من الكتابة'}
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {/* إذا لم يكن هناك مستخدمين */}
                  {seats.filter(user => user !== null).length === 0 && (
                    <div className="text-center py-6">
                      <div className="text-4xl mb-2">👥</div>
                      <p className="text-white/70 text-sm">لا يوجد مستخدمين متصلين</p>

                      {/* خيار حذف الغرفة الحالية للإدارة */}
                      {userData?.isAdmin && currentRoom && (
                        <div className="mt-4">
                          <button
                            onClick={() => deleteRoom(currentRoom._id || currentRoom.id, currentRoom.name)}
                            className="px-4 py-2 bg-red-500/20 hover:bg-red-500/40 text-red-300 rounded-lg text-sm transition-colors border border-red-400/30"
                          >
                            🗑️ حذف هذه الغرفة
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-4 sm:space-y-6">
            {/* Voice Room */}
            <div className="bg-black/20 backdrop-blur-md rounded-xl p-3 sm:p-4 shadow-xl border border-white/10 relative overflow-hidden">
              <div className="text-center mb-2 sm:mb-3">
                <h2 className="text-lg sm:text-xl font-bold text-yellow-400 mb-1">{currentRoom.name}</h2>
                {currentUser.isOwner && (
                  <p className="text-xs text-yellow-300 opacity-80">👑 أنت مدير الغرفة - اضغط على المستخدمين لإدارتهم</p>
                )}
                {!currentUser.isOwner && (
                  <p className="text-xs text-white/70 opacity-80">اضغط على اسمك لكتم/إلغاء كتم المايك</p>
                )}

                {/* عرض المتحدث الحالي */}
                {(() => {
                  const activeSpeaker = seats.find(user => user && !user.isMuted && user.isOnMic);
                  return activeSpeaker ? (
                    <div className="mt-2 p-2 bg-green-500/20 rounded-lg border border-green-400/30">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-8 h-8 rounded-full overflow-hidden">
                          {activeSpeaker.avatar && activeSpeaker.avatar.startsWith('data:') ? (
                            <img
                              src={activeSpeaker.avatar}
                              alt={activeSpeaker.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-green-500 flex items-center justify-center text-white">
                              {activeSpeaker.avatar || '👤'}
                            </div>
                          )}
                        </div>
                        <span className="text-green-300 font-semibold text-sm">
                          🎤 {activeSpeaker.name} يتحدث
                        </span>
                        {activeSpeaker.isOwner && <Crown className="h-3 w-3 text-yellow-400" />}
                      </div>
                    </div>
                  ) : (
                    <div className="mt-2 p-2 bg-gray-500/20 rounded-lg border border-gray-400/30">
                      <span className="text-gray-300 text-sm">🔇 لا يوجد متحدث حالياً</span>
                    </div>
                  );
                })()}
              </div>

              {/* Animated Mic Emojis */}
              {micEmojis.map((emoji) => (
                <div
                  key={emoji.id}
                  className="absolute pointer-events-none text-2xl z-10"
                  style={{
                    left: `calc(50% + ${emoji.x}px)`,
                    bottom: '120px',
                    transform: 'translateY(0px)',
                    opacity: 1,
                    animation: 'floatUp 3s ease-out forwards'
                  }}
                >
                  {emoji.emoji}
                </div>
              ))}

              {/* Linear Seats Layout */}
              <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-4 sm:mb-6 px-2">
                {seats.map((user, index) => (
                  <div
                    key={index}
                    className="relative group"
                  >
                    <div
                      className={`w-12 h-12 sm:w-16 sm:h-16 rounded-full border-2 transition-all duration-300 cursor-pointer ${
                        user
                          ? user.isMuted
                            ? 'bg-red-600 border-red-400 shadow-lg'
                            : 'bg-green-600 border-green-400 shadow-lg'
                          : 'bg-gray-700 border-gray-500 hover:border-yellow-400 hover:bg-gray-600'
                      } ${user?.isDeafened ? 'opacity-50' : ''}`}
                      onClick={() => {
                        if (!currentRoom || !(currentRoom._id || currentRoom.id)) return; // تعطيل الانضمام إذا لم تكن بيانات الغرفة جاهزة
                        if (user && user.id === currentUser.id) {
                          // إذا كان المستخدم الحالي، تبديل كتم المايك
                          toggleMute(user.id);
                        } else if (!user && !currentUser.isOnMic) {
                          // إذا كان المقعد فارغ والمستخدم ليس على المايك، الانضمام
                          joinMicSeat(index);
                        } else if (user && currentUser.isOwner) {
                          // إذا كان المستخدم مالك الغرفة، إظهار خيارات الإدارة
                          // يمكن إضافة قائمة منسدلة هنا لاحقاً
                        }
                      }}
                      title={
                        !currentRoom || !(currentRoom._id || currentRoom.id)
                          ? 'جاري تحميل بيانات الغرفة...'
                          : user
                          ? user.id === currentUser.id
                            ? 'اضغط لكتم/إلغاء كتم المايك'
                            : currentUser.isOwner
                              ? 'خيارات الإدارة'
                              : user.name
                          : !currentUser.isOnMic
                            ? 'اضغط للانضمام للمايك'
                            : 'مقعد فارغ'
                      }
                      style={{ pointerEvents: !currentRoom || !(currentRoom._id || currentRoom.id) ? 'none' : 'auto', opacity: !currentRoom || !(currentRoom._id || currentRoom.id) ? 0.5 : 1 }}
                    >
                      <div className="h-full flex flex-col items-center justify-center relative">
                        {user ? (
                          <>
                            {user.avatar && user.avatar.startsWith('data:') ? (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="w-8 h-8 sm:w-12 sm:h-12 rounded-full object-cover"
                              />
                            ) : (
                              <div className="text-lg sm:text-2xl">{user.avatar || '👤'}</div>
                            )}
                            {user.isOwner && <Crown className="h-2 w-2 sm:h-3 sm:w-3 text-yellow-300 absolute -top-1" />}
                            {user.level && (
                              <div className="absolute -bottom-4 sm:-bottom-6 bg-blue-500 text-white text-xs px-1 rounded font-bold">
                                Lv{user.level}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-lg sm:text-2xl text-gray-400">
                            {!currentUser.isOnMic ? '🎤' : '👤'}
                          </div>
                        )}
                      </div>
                    </div>

                    {user && user.name && (
                      <div className="absolute -bottom-8 sm:-bottom-10 left-1/2 transform -translate-x-1/2 text-white text-xs text-center whitespace-nowrap">
                        {user.name}
                        {user.isChatMuted && <span className="text-red-400 ml-1">🚫</span>}
                      </div>
                    )}

                    {!user && !currentUser.isOnMic && (
                      <div className="absolute -bottom-8 sm:-bottom-10 left-1/2 transform -translate-x-1/2 text-yellow-400 text-xs text-center whitespace-nowrap">
                        انضم للمايك
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* تم إزالة أزرار التحكم الصوتي - التحكم من خلال الضغط على المقاعد */}

            </div>

            {/* Text Chat */}
            <div className="bg-black/20 backdrop-blur-md rounded-xl p-3 sm:p-6 shadow-xl border border-white/10">

              {/* Chat Messages Container - مساحة أكبر */}
              <div
                ref={chatContainerRef}
                className="bg-black/30 rounded-xl p-4 sm:p-6 h-80 sm:h-96 md:h-[500px] mb-4 sm:mb-6 space-y-3 sm:space-y-4 border border-white/10 overflow-y-auto scrollbar-hide"
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none',
                }}
              >
                {chatMessages.map((message) => (
                  <div key={message.id} className="bg-black/40 rounded-lg p-2 sm:p-3 border border-white/10 animate-fadeIn">
                    <div className="flex items-start space-x-2 sm:space-x-3">
                      <div className="flex-shrink-0">
                        {message.user.avatar && message.user.avatar.startsWith('data:') ? (
                          <img
                            src={message.user.avatar}
                            alt={message.user.name}
                            className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="text-lg sm:text-2xl">{message.user.avatar || '👤'}</div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                          <span className={`font-semibold text-sm sm:text-base truncate ${
                            message.user.id === 'system' ? 'text-orange-400' : 'text-yellow-400'
                          }`}>
                            {message.user.name}
                          </span>
                          <span className="text-xs text-white/50 flex-shrink-0">
                            {message.timestamp.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                          </span>
                        </div>
                        <p className="text-white/90 text-xs sm:text-sm leading-relaxed break-words">{message.message}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="space-y-2">
                {showEmojiPicker && (
                  <div className="bg-black/40 rounded-lg p-3 border border-white/20">
                    <div className="grid grid-cols-8 sm:grid-cols-10 gap-2">
                      {chatEmojis.map((emoji, index) => (
                        <button
                          key={index}
                          onClick={() => addEmojiToMessage(emoji)}
                          className="text-lg sm:text-xl p-1 hover:bg-white/10 rounded transition-colors"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* الألعاب السريعة - قابلة للطي */}
                {showGameShortcuts && (
                  <div className="mb-4">
                    <div className="grid grid-cols-3 sm:grid-cols-5 gap-2 animate-fadeIn">
                      {gameShortcuts.map((game) => (
                        <button
                          key={game.id}
                          onClick={() => {
                            if (game.url === '#') {
                              const gameMessage = {
                                id: Date.now().toString(),
                                user: currentUser,
                                message: `🎮 ${game.name} - Coming Soon!`,
                                timestamp: new Date()
                              };
                              setChatMessages(prev => [...prev, gameMessage]);
                              return;
                            }

                            const isMobile = window.innerWidth <= 768;
                            let windowFeatures = '';

                            if (isMobile) {
                              const screenWidth = window.screen.availWidth;
                              const screenHeight = window.screen.availHeight;
                              windowFeatures = `width=${screenWidth},height=${screenHeight},left=0,top=0,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no`;
                            } else {
                              windowFeatures = 'width=1200,height=800,left=100,top=100,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no';
                            }

                            const gameWindow = window.open(game.url, '_blank', windowFeatures);

                            const gameMessage = {
                              id: Date.now().toString(),
                              user: currentUser,
                              message: `🎮 Started playing ${game.name}`,
                              timestamp: new Date()
                            };
                            setChatMessages(prev => [...prev, gameMessage]);

                            if (gameWindow) {
                              gameWindow.focus();
                            }

                            setShowGameShortcuts(false);
                          }}
                          className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transition-all duration-200 text-xs flex flex-col items-center space-y-1 transform hover:scale-105"
                          title={game.name}
                        >
                          <span className="text-lg">{game.icon}</span>
                          <span className="text-white font-medium truncate w-full text-center leading-tight">
                            {game.name.length > 8 ? game.name.substring(0, 6) + '...' : game.name}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* الترتيب الجديد للأزرار */}
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    placeholder={currentUser.isChatMuted ? "تم منعك من الكتابة" : "اكتب رسالتك هنا"}
                    disabled={currentUser.isChatMuted}
                    className={`flex-1 bg-black/30 border border-white/20 rounded-lg px-3 py-2 sm:px-4 sm:py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent placeholder-white/50 text-sm sm:text-base ${
                      currentUser.isChatMuted ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  />

                  {/* 1. Send Button - ملتصق بحقل النص */}
                  <button
                    onClick={sendMessage}
                    className="bg-yellow-500 hover:bg-yellow-600 px-3 py-2 sm:px-4 sm:py-3 rounded-lg font-semibold transition-colors shadow-lg text-black flex-shrink-0"
                  >
                    <Send className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>

                  {/* 2. Sound/Speaker Button */}
                  <button
                    onClick={toggleSpeaker}
                    className={`p-2 sm:p-3 rounded-lg transition-colors shadow-lg flex-shrink-0 ${
                      isAudioEnabled
                        ? 'bg-blue-500 hover:bg-blue-600'
                        : 'bg-gray-500 hover:bg-gray-600'
                    }`}
                    title={isAudioEnabled ? 'إيقاف السماعات' : 'تشغيل السماعات'}
                  >
                    <Volume2 className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </button>

                  {/* 3. Microphone Button */}
                  <button
                    onClick={toggleMicrophone}
                    disabled={isConnecting}
                    className={`p-2 sm:p-3 rounded-lg transition-colors shadow-lg flex-shrink-0 ${
                      isConnecting
                        ? 'bg-gray-500 cursor-not-allowed'
                        : isMicEnabled
                          ? 'bg-green-500 hover:bg-green-600'
                          : 'bg-red-500 hover:bg-red-600'
                    }`}
                    title={isConnecting ? 'جاري الاتصال...' : isMicEnabled ? 'إيقاف الميكروفون' : 'تشغيل الميكروفون'}
                  >
                    {isConnecting ? (
                      <div className="animate-spin h-4 w-4 sm:h-5 sm:w-5 border-2 border-white border-t-transparent rounded-full"></div>
                    ) : (
                      <Mic className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                    )}
                  </button>

                  {/* 4. Quick Games Button - أصغر حجماً */}
                  <button
                    onClick={() => setShowGameShortcuts(!showGameShortcuts)}
                    className="bg-purple-500 hover:bg-purple-600 p-2 sm:p-3 rounded-lg transition-colors shadow-lg flex-shrink-0"
                    title="الألعاب السريعة"
                  >
                    <Gamepad2 className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>

      {/* Create Room Modal */}
      {showCreateRoom && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-black/30 backdrop-blur-md rounded-2xl p-4 sm:p-8 max-w-md w-full shadow-2xl border border-white/20">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 sm:mb-6 text-center">انشاء غرفة جديدة</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold mb-2">اسم الغرفة</label>
                <input
                  type="text"
                  value={newRoomName}
                  onChange={(e) => setNewRoomName(e.target.value)}
                  className="w-full bg-black/30 border border-white/20 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="ادخل اسم الغرفة"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold">غرفة خاصة</label>
                <input
                  type="checkbox"
                  checked={isNewRoomLocked}
                  onChange={(e) => setIsNewRoomLocked(e.target.checked)}
                  className="rounded"
                />
              </div>

              {isNewRoomLocked && (
                <div>
                  <label className="block text-sm font-semibold mb-2">كلمة المرور</label>
                  <input
                    type="password"
                    value={newRoomPassword}
                    onChange={(e) => setNewRoomPassword(e.target.value)}
                    className="w-full bg-black/30 border border-white/20 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    placeholder="ادخل كلمة المرور"
                  />
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={createRoom}
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 py-2 sm:py-3 rounded-lg font-semibold transition-colors duration-200 shadow-lg text-black"
                >
                  انشاء الغرفة
                </button>
                <button
                  onClick={() => setShowCreateRoom(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 py-2 sm:py-3 rounded-lg font-semibold transition-colors duration-200 text-white"
                >
                  الغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تخصيص اسم الغرفة */}
      {showCustomizeRoom && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-black/30 backdrop-blur-md rounded-2xl p-4 sm:p-8 max-w-md w-full shadow-2xl border border-white/20">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 sm:mb-6 text-center">تخصيص اسم الغرفة</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold mb-2">اسم الغرفة الجديد</label>
                <input
                  type="text"
                  value={customRoomName}
                  onChange={(e) => setCustomRoomName(e.target.value)}
                  className="w-full bg-black/30 border border-white/20 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="ادخل اسم الغرفة الجديد"
                  maxLength={50}
                />
                <p className="text-white/50 text-xs mt-1">الحد الأقصى: 50 حرف</p>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={updateRoomName}
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 py-2 sm:py-3 rounded-lg font-semibold transition-colors duration-200 shadow-lg text-black"
                >
                  حفظ الاسم
                </button>
                <button
                  onClick={() => {
                    setShowCustomizeRoom(false);
                    setCustomRoomName('');
                  }}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 py-2 sm:py-3 rounded-lg font-semibold transition-colors duration-200 text-white"
                >
                  الغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleVoiceRooms;