<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار WebRTC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(34, 197, 94, 0.3); border: 1px solid #22c55e; }
        .error { background: rgba(239, 68, 68, 0.3); border: 1px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.3); border: 1px solid #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.3); border: 1px solid #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #6b7280; cursor: not-allowed; }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 اختبار نظام الغرف الصوتية</h1>
        
        <div class="test-section">
            <h2>1. اختبار دعم المتصفح</h2>
            <div id="browser-support"></div>
        </div>

        <div class="test-section">
            <h2>2. اختبار الوصول للمايكروفون</h2>
            <button onclick="testMicrophone()">اختبار المايكروفون</button>
            <div id="mic-status"></div>
        </div>

        <div class="test-section">
            <h2>3. اختبار اتصال Socket.IO</h2>
            <button onclick="testSocketIO()">اختبار Socket.IO</button>
            <div id="socket-status"></div>
        </div>

        <div class="test-section">
            <h2>4. اختبار WebRTC</h2>
            <button onclick="testWebRTC()">اختبار WebRTC</button>
            <div id="webrtc-status"></div>
        </div>

        <div class="test-section">
            <h2>📋 سجل الاختبارات</h2>
            <div id="test-log" class="log"></div>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `<div style="color: ${getLogColor(type)}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getLogColor(type) {
            switch(type) {
                case 'success': return '#22c55e';
                case 'error': return '#ef4444';
                case 'warning': return '#f59e0b';
                default: return '#ffffff';
            }
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 1. اختبار دعم المتصفح
        function checkBrowserSupport() {
            log('بدء اختبار دعم المتصفح...');
            
            const features = {
                'WebRTC': !!window.RTCPeerConnection,
                'getUserMedia': !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
                'Socket.IO': !!window.io,
                'Web Audio API': !!window.AudioContext || !!window.webkitAudioContext
            };

            let allSupported = true;
            let statusHtml = '';

            for (const [feature, supported] of Object.entries(features)) {
                const status = supported ? 'مدعوم ✅' : 'غير مدعوم ❌';
                const type = supported ? 'success' : 'error';
                statusHtml += `<div class="status ${type}">${feature}: ${status}</div>`;
                
                if (!supported) allSupported = false;
                log(`${feature}: ${status}`, supported ? 'success' : 'error');
            }

            updateStatus('browser-support', statusHtml, allSupported ? 'success' : 'error');
            return allSupported;
        }

        // 2. اختبار المايكروفون
        async function testMicrophone() {
            log('بدء اختبار المايكروفون...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                updateStatus('mic-status', '✅ تم الوصول للمايكروفون بنجاح', 'success');
                log('تم الوصول للمايكروفون بنجاح', 'success');
                
                // إيقاف الستريم
                stream.getTracks().forEach(track => track.stop());
                
                return true;
            } catch (error) {
                const errorMsg = `❌ فشل في الوصول للمايكروفون: ${error.message}`;
                updateStatus('mic-status', errorMsg, 'error');
                log(errorMsg, 'error');
                return false;
            }
        }

        // 3. اختبار Socket.IO
        function testSocketIO() {
            log('بدء اختبار Socket.IO...');
            
            try {
                const socket = io();
                
                socket.on('connect', () => {
                    updateStatus('socket-status', '✅ اتصال Socket.IO ناجح', 'success');
                    log('اتصال Socket.IO ناجح', 'success');
                    socket.disconnect();
                });

                socket.on('connect_error', (error) => {
                    const errorMsg = `❌ فشل اتصال Socket.IO: ${error.message}`;
                    updateStatus('socket-status', errorMsg, 'error');
                    log(errorMsg, 'error');
                });

                // timeout بعد 5 ثواني
                setTimeout(() => {
                    if (!socket.connected) {
                        const errorMsg = '❌ انتهت مهلة اتصال Socket.IO';
                        updateStatus('socket-status', errorMsg, 'error');
                        log(errorMsg, 'error');
                        socket.disconnect();
                    }
                }, 5000);

            } catch (error) {
                const errorMsg = `❌ خطأ في Socket.IO: ${error.message}`;
                updateStatus('socket-status', errorMsg, 'error');
                log(errorMsg, 'error');
            }
        }

        // 4. اختبار WebRTC
        async function testWebRTC() {
            log('بدء اختبار WebRTC...');
            
            try {
                // إنشاء RTCPeerConnection
                const pc = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });

                // اختبار إنشاء offer
                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);
                
                updateStatus('webrtc-status', '✅ WebRTC يعمل بشكل صحيح', 'success');
                log('WebRTC يعمل بشكل صحيح', 'success');
                
                pc.close();
                return true;
                
            } catch (error) {
                const errorMsg = `❌ فشل اختبار WebRTC: ${error.message}`;
                updateStatus('webrtc-status', errorMsg, 'error');
                log(errorMsg, 'error');
                return false;
            }
        }

        // تشغيل اختبار دعم المتصفح عند تحميل الصفحة
        window.onload = function() {
            log('بدء اختبارات النظام...', 'info');
            checkBrowserSupport();
        };
    </script>
</body>
</html>
