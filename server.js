import { createRequire } from 'module';
import path from 'path';
import http from 'http';
import { WebSocketServer } from 'ws';
import { fileURLToPath } from 'url';

// تمكين require داخل بيئة ES Module
const require = createRequire(import.meta.url);

// تحميل متغيرات البيئة
require('dotenv').config();

// حساب __dirname في ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// استيراد الحزم عبر require
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cron = require('node-cron');
const { Server: SocketIOServer } = require('socket.io');

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد CORS
app.use(cors({
  origin: ['https://infinity-box25.netlify.app', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// تقديم ملفات الواجهة من مجلد build (بعد تشغيل npm run build)
app.use(express.static(path.join(__dirname, 'dist', 'public')));

// اتصال MongoDB
mongoose.connect(process.env.MONGODB_URI).then(async () => {
  console.log('✅ Connected to MongoDB Atlas');

  // حذف فهرس inviteCode المشكل
  try {
    await User.collection.dropIndex('inviteCode_1');
    console.log('🗑️ Dropped problematic inviteCode index');
  } catch (error) {
    // الفهرس قد لا يكون موجود، هذا طبيعي
    console.log('ℹ️ inviteCode index not found or already dropped');
  }
}).catch(err => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});

// نموذج المستخدم
const userSchema = new mongoose.Schema({
  playerId: {
    type: String,
    unique: true,
    required: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  email: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  avatar: String,
  profileImage: String,
  gender: {
    type: String,
    enum: ['male', 'female'],
    default: 'male'
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  coins: {
    type: Number,
    default: 0
  },
  goldCoins: {
    type: Number,
    default: 10000
  },
  pearls: {
    type: Number,
    default: 10
  },
  level: {
    type: Number,
    default: 1
  },
  experience: {
    type: Number,
    default: 0
  },
  activeSessionToken: String,
  joinedAt: {
    type: Date,
    default: Date.now
  },
  lastActive: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['online', 'offline', 'away'],
    default: 'offline'
  }
}, {
  timestamps: true
});

// توليد Player ID فريد عند الإنشاء (مرن - يبدأ من 1)
userSchema.pre('save', async function(next) {
  if (this.isNew && !this.playerId) {
    let playerId;
    let isUnique = false;

    // البحث عن أعلى Player ID موجود
    const lastUser = await User.findOne({}, {}, { sort: { 'playerId': -1 } });
    let nextId = 1;

    if (lastUser && lastUser.playerId) {
      const lastIdNum = parseInt(lastUser.playerId);
      if (!isNaN(lastIdNum)) {
        nextId = lastIdNum + 1;
      }
    }

    // التأكد من عدم وجود Player ID مكرر
    while (!isUnique) {
      playerId = nextId.toString();
      const existing = await User.findOne({ playerId });
      if (!existing) {
        isUnique = true;
      } else {
        nextId++;
      }
    }

    this.playerId = playerId;
  }
  next();
});

const User = mongoose.model('User', userSchema);

// نموذج الأصدقاء
const friendshipSchema = new mongoose.Schema({
  requester: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  requesterPlayerId: { type: String, required: true }, // Player ID الصغير
  recipient: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  recipientPlayerId: { type: String, required: true }, // Player ID الصغير
  status: { type: String, enum: ['pending', 'accepted', 'declined', 'blocked'], default: 'pending' },
  requestedAt: { type: Date, default: Date.now },
  respondedAt: { type: Date }
}, {
  timestamps: true
});

const Friendship = mongoose.model('Friendship', friendshipSchema);

// نموذج الهدايا
const giftSchema = new mongoose.Schema({
  sender: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  recipient: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  giftType: { type: String, enum: ['gold', 'pearls', 'item'], required: true },
  amount: { type: Number, required: true },
  message: { type: String },
  itemType: { type: String }, // نوع العنصر إذا كانت الهدية عنصر
  status: { type: String, enum: ['sent', 'received'], default: 'sent' }
}, {
  timestamps: true
});

const Gift = mongoose.model('Gift', giftSchema);

// نموذج المعاملات
const transactionSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['charge', 'gift_sent', 'gift_received', 'shield_purchase', 'game_win', 'game_loss', 'exchange'], required: true },
  amount: { type: Number, required: true },
  currency: { type: String, enum: ['gold', 'pearls'], required: true },
  description: { type: String },
  relatedUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  status: { type: String, enum: ['pending', 'completed', 'failed'], default: 'completed' }
}, {
  timestamps: true
});

const Transaction = mongoose.model('Transaction', transactionSchema);

// نموذج الدروع
const shieldSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['gold', 'usd'], required: true },
  isActive: { type: Boolean, default: true },
  activatedAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, required: true },
  cost: { type: Number, required: true },
  currency: { type: String, enum: ['gold', 'usd'], required: true }
}, {
  timestamps: true
});

const Shield = mongoose.model('Shield', shieldSchema);

// نموذج الإشعارات
const notificationSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  userPlayerId: { type: String, required: true }, // Player ID الصغير
  type: { type: String, enum: ['gift_received', 'friend_request', 'friend_accepted', 'message', 'item_received'], required: true },
  title: { type: String, required: true },
  message: { type: String, required: true },
  data: { type: Object }, // بيانات إضافية
  isRead: { type: Boolean, default: false },
  fromUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // MongoDB ObjectId للتوافق
  fromUserPlayerId: { type: String } // Player ID الصغير
}, {
  timestamps: true
});

const Notification = mongoose.model('Notification', notificationSchema);

// نموذج الرسائل
const messageSchema = new mongoose.Schema({
  sender: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  senderPlayerId: { type: String, required: true }, // Player ID الصغير
  recipient: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  recipientPlayerId: { type: String, required: true }, // Player ID الصغير
  content: { type: String, required: true },
  isRead: { type: Boolean, default: false },
  messageType: { type: String, enum: ['text', 'gift', 'item'], default: 'text' }
}, {
  timestamps: true
});

const Message = mongoose.model('Message', messageSchema);

// نموذج الغرف الصوتية - تم نقله للأسفل

// وظيفة إنشاء إشعار
const createNotification = async (userId, type, title, message, data = {}, fromUserId = null, userPlayerId = null) => {
  try {
    // إذا لم يتم تمرير userPlayerId، نحاول الحصول عليه من المستخدم
    if (!userPlayerId && userId) {
      const user = await User.findById(userId);
      if (user) {
        userPlayerId = user.playerId;
      }
    }

    // إذا لم يتم تمرير fromUserId، نحاول الحصول على Player ID للمرسل
    let fromUserPlayerId = null;
    if (fromUserId) {
      const fromUser = await User.findById(fromUserId);
      if (fromUser) {
        fromUserPlayerId = fromUser.playerId;
      }
    }

    const notification = new Notification({
      user: userId, // MongoDB ObjectId للتوافق
      userPlayerId: userPlayerId, // Player ID الصغير
      type,
      title,
      message,
      data,
      fromUser: fromUserId, // MongoDB ObjectId للتوافق
      fromUserPlayerId: fromUserPlayerId // Player ID الصغير
    });
    await notification.save();
    console.log(`📢 Notification created for user ${userPlayerId || userId}: ${title}`);
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
  }
};

// middleware للمصادقة
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'infinitybox_secret_key', (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// نموذج إحصائيات الألعاب
const GameStatsSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  gameType: { type: String, required: true },
  sessionId: { type: String, required: true },
  startTime: { type: Date, required: true },
  endTime: { type: Date },
  duration: { type: Number },
  betAmount: { type: Number, default: 0 },
  winAmount: { type: Number, default: 0 },
  lossAmount: { type: Number, default: 0 },
  netResult: { type: Number, default: 0 },
  gamesPlayed: { type: Number, default: 0 },
  playerScore: { type: Number, default: 0 },
  skillFactor: { type: Number, default: 0 },
  economicFactor: { type: Number, default: 0 },
  winProbability: { type: Number, default: 0 }
}, { timestamps: true });

const GameStats = mongoose.model('GameStats', GameStatsSchema);

// نموذج الغرف الصوتية الشخصية
const VoiceRoomSchema = new mongoose.Schema({
  name: { type: String, required: true }, // اسم الغرفة المخصص من المالك
  ownerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // مالك الغرفة (MongoDB ObjectId)
  ownerPlayerId: { type: String, required: false, unique: true }, // معرف اللاعب الصغير (مثل 123456) - جعله اختياري مؤقتاً
  ownerName: { type: String, required: true }, // اسم المالك للعرض
  participants: [{
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    playerId: { type: String }, // معرف اللاعب الصغير
    username: { type: String },
    avatar: { type: String, default: '👤' },
    isMuted: { type: Boolean, default: false },
    isSpeaking: { type: Boolean, default: false },
    seatIndex: { type: Number }, // رقم المقعد (1-10) أو null للمحادثة النصية فقط
    joinedAt: { type: Date, default: Date.now }
  }],
  maxParticipants: { type: Number, default: 10 }, // زيادة العدد إلى 10
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  lastActivity: { type: Date, default: Date.now }
}, { timestamps: true });

const VoiceRoom = mongoose.model('VoiceRoom', VoiceRoomSchema);

// نموذج رسائل الدردشة في الغرف الصوتية
const VoiceChatSchema = new mongoose.Schema({
  roomId: { type: String, required: true }, // يمكن أن يكون Player ID أو MongoDB ObjectId
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // MongoDB ObjectId للتوافق
  userPlayerId: { type: String, required: true }, // Player ID الصغير
  username: { type: String, required: true },
  avatar: { type: String, default: '👤' },
  message: { type: String, required: true },
  type: { type: String, enum: ['user', 'system'], default: 'user' },
  timestamp: { type: Date, default: Date.now }
}, { timestamps: true });

const VoiceChat = mongoose.model('VoiceChat', VoiceChatSchema);

// Routes الأساسية

// فحص صحة الخادم
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

app.get('/', (req, res) => {
  res.json({ 
    message: 'INFINITY BOX Backend API v2.0 - Working!',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users', 
      health: '/health'
    }
  });
});

// تسجيل مستخدم جديد
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password, email } = req.body;

    // التحقق من وجود المستخدم
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });

    if (existingUser) {
      return res.status(400).json({ 
        message: 'اسم المستخدم أو البريد الإلكتروني مُستخدم بالفعل' 
      });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);

    // توليد معرف لاعب فريد (مرن - يبدأ من 1)
    let playerId;
    let isUnique = false;

    // البحث عن أعلى Player ID موجود
    const lastUser = await User.findOne({}, {}, { sort: { 'playerId': -1 } });
    let nextId = 1;

    if (lastUser && lastUser.playerId) {
      const lastIdNum = parseInt(lastUser.playerId);
      if (!isNaN(lastIdNum)) {
        nextId = lastIdNum + 1;
      }
    }

    // التأكد من عدم وجود Player ID مكرر
    while (!isUnique) {
      playerId = nextId.toString();
      const existingPlayerId = await User.findOne({ playerId });
      if (!existingPlayerId) {
        isUnique = true;
      } else {
        nextId++;
      }
    }

    // توليد كود دعوة فريد
    const inviteCode = `INV${playerId}${Date.now().toString().slice(-4)}`;

    // إنشاء المستخدم الجديد
    const newUser = new User({
      username,
      password: hashedPassword,
      email,
      playerId,
      inviteCode,
      goldCoins: 10000, // مكافأة الترحيب
      pearls: 10, // مكافأة الترحيب - 10 لؤلؤ
      level: 1,
      experience: 0,
      isAdmin: false,
      status: 'online'
    });

    await newUser.save();

    // إنشاء غرفة صوتية شخصية للمستخدم الجديد
    try {
      const personalRoom = new VoiceRoom({
        name: `غرفة ${newUser.username}`, // اسم افتراضي للغرفة
        ownerId: newUser._id,
        ownerPlayerId: newUser.playerId, // استخدام Player ID الصغير
        ownerName: newUser.username,
        participants: [],
        maxParticipants: 10,
        isActive: true
      });

      await personalRoom.save();
      console.log(`🎤 تم إنشاء غرفة صوتية شخصية للمستخدم: ${newUser.username} (Player ID: ${newUser.playerId})`);
    } catch (roomError) {
      console.error('❌ خطأ في إنشاء الغرفة الصوتية للمستخدم الجديد:', roomError);
      // لا نوقف عملية التسجيل إذا فشل إنشاء الغرفة
    }

    // إنشاء JWT token
    const token = jwt.sign(
      { userId: newUser._id, username: newUser.username },
      process.env.JWT_SECRET || 'infinitybox_secret_key',
      { expiresIn: '24h' }
    );

    // تحديث activeSessionToken
    newUser.activeSessionToken = token;
    await newUser.save();

    res.status(201).json({
      message: 'تم إنشاء الحساب بنجاح',
      welcomeMessage: `🎉 مرحباً ${newUser.username}! لقد حصلت على مكافأة الترحيب: 10,000 عملة ذهبية و 10 لآلئ! استمتع باللعب!`,
      isNewUser: true,
      rewards: {
        goldCoins: 10000,
        pearls: 10,
        message: 'مكافأة الترحيب'
      },
      token,
      user: {
        id: newUser._id,
        playerId: newUser.playerId,
        username: newUser.username,
        email: newUser.email,
        goldCoins: newUser.goldCoins,
        pearls: newUser.pearls,
        level: newUser.level,
        isAdmin: newUser.isAdmin
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'خطأ في إنشاء الحساب' });
  }
});

// تسجيل الدخول
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // البحث عن المستخدم
    const user = await User.findOne({ username });
    if (!user) {
      return res.status(400).json({ 
        message: 'المستخدم غير موجود. الرجاء التسجيل أولاً.' 
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ message: 'كلمة المرور غير صحيحة' });
    }

    // إنشاء JWT token جديد
    const token = jwt.sign(
      { userId: user._id, username: user.username },
      process.env.JWT_SECRET || 'infinitybox_secret_key',
      { expiresIn: '24h' }
    );

    // تحديث الجلسة والنشاط
    user.activeSessionToken = token;
    user.lastActive = new Date();
    user.status = 'online';
    await user.save();

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: user._id,
        playerId: user.playerId,
        username: user.username,
        email: user.email,
        goldCoins: user.goldCoins,
        pearls: user.pearls,
        level: user.level,
        isAdmin: user.isAdmin,
        profileImage: user.profileImage,
        gender: user.gender
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'خطأ في تسجيل الدخول' });
  }
});

// جلب بيانات المستخدم الحالي
app.get('/api/user', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({
      id: user._id,
      playerId: user.playerId,
      username: user.username,
      email: user.email,
      goldCoins: user.goldCoins,
      pearls: user.pearls,
      level: user.level,
      isAdmin: user.isAdmin,
      profileImage: user.profileImage,
      gender: user.gender,
      status: user.status,
      lastActive: user.lastActive
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات المستخدم' });
  }
});

// جلب العملات الحالية للمستخدم
app.get('/api/user/currency', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('goldCoins pearls');
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({
      goldCoins: user.goldCoins,
      pearls: user.pearls
    });
  } catch (error) {
    console.error('Get currency error:', error);
    res.status(500).json({ message: 'خطأ في جلب العملات' });
  }
});

// تحديث العملات للمستخدم
app.put('/api/user/currency', authenticateToken, async (req, res) => {
  try {
    const { goldCoins, pearls } = req.body;

    // التحقق من صحة البيانات
    if (typeof goldCoins !== 'number' || typeof pearls !== 'number') {
      return res.status(400).json({ message: 'قيم العملات يجب أن تكون أرقام' });
    }

    if (goldCoins < 0 || pearls < 0) {
      return res.status(400).json({ message: 'قيم العملات لا يمكن أن تكون سالبة' });
    }

    const user = await User.findByIdAndUpdate(
      req.user.userId,
      {
        goldCoins: Math.floor(goldCoins),
        pearls: Math.floor(pearls)
      },
      { new: true }
    ).select('goldCoins pearls');

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({
      goldCoins: user.goldCoins,
      pearls: user.pearls,
      message: 'تم تحديث العملات بنجاح'
    });
  } catch (error) {
    console.error('Update currency error:', error);
    res.status(500).json({ message: 'خطأ في تحديث العملات' });
  }
});

// جلب بيانات المستخدم الحالي (endpoint للأدمن)
app.get('/api/users/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({
      id: user._id,
      playerId: user.playerId,
      username: user.username,
      email: user.email,
      goldCoins: user.goldCoins,
      pearls: user.pearls,
      level: user.level,
      isAdmin: user.isAdmin,
      profileImage: user.profileImage,
      gender: user.gender,
      status: user.status,
      lastActive: user.lastActive
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات المستخدم' });
  }
});

// تحديث الملف الشخصي للمستخدم الحالي
app.put('/api/profile/update', authenticateToken, async (req, res) => {
  try {
    const { profileImage, gender, username, email } = req.body;

    console.log('🔄 Profile update request for user:', req.user.userId);
    console.log('📝 Update data:', {
      hasProfileImage: !!profileImage,
      gender,
      username,
      email
    });

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديث البيانات المرسلة فقط
    if (profileImage !== undefined) {
      user.profileImage = profileImage;
      console.log('📸 Profile image updated');
    }

    if (gender !== undefined) {
      user.gender = gender;
      console.log('👤 Gender updated to:', gender);
    }

    if (username !== undefined) {
      // التحقق من عدم وجود اسم المستخدم مع مستخدم آخر
      const existingUser = await User.findOne({ username, _id: { $ne: req.user.userId } });
      if (existingUser) {
        return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
      }
      user.username = username;
      console.log('📝 Username updated to:', username);
    }

    if (email !== undefined) {
      // التحقق من عدم وجود البريد الإلكتروني مع مستخدم آخر
      const existingUser = await User.findOne({ email, _id: { $ne: req.user.userId } });
      if (existingUser) {
        return res.status(400).json({ message: 'البريد الإلكتروني موجود بالفعل' });
      }
      user.email = email;
      console.log('📧 Email updated to:', email);
    }

    // حفظ التغييرات
    await user.save();
    console.log('✅ Profile updated successfully for user:', user.username);

    // إرجاع البيانات المحدثة
    res.json({
      id: user._id,
      playerId: user.playerId,
      username: user.username,
      email: user.email,
      goldCoins: user.goldCoins,
      pearls: user.pearls,
      level: user.level,
      isAdmin: user.isAdmin,
      profileImage: user.profileImage,
      gender: user.gender,
      status: user.status,
      lastActive: user.lastActive
    });
  } catch (error) {
    console.error('❌ Profile update error:', error);
    res.status(500).json({ message: 'خطأ في تحديث الملف الشخصي' });
  }
});

// تحديث صورة الملف الشخصي
app.post('/api/user/update-profile-image', authenticateToken, async (req, res) => {
  try {
    const { profileImage } = req.body;
    const userId = req.user.userId;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    user.profileImage = profileImage;
    await user.save();

    res.json({
      message: 'تم تحديث الصورة الشخصية بنجاح',
      profileImage: user.profileImage
    });

  } catch (error) {
    console.error('Update profile image error:', error);
    res.status(500).json({ message: 'خطأ في تحديث الصورة' });
  }
});

// ========== ADMIN ROUTES ==========

// جلب جميع المستخدمين (للأدمن فقط)
app.get('/api/admin/users', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const users = await User.find({}).select('-password').sort({ createdAt: -1 });
    res.json({ users });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({ message: 'خطأ في جلب المستخدمين' });
  }
});

// البحث عن مستخدم (للأدمن فقط)
app.get('/api/users/search', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { username } = req.query;
    if (!username) {
      return res.status(400).json({ message: 'اسم المستخدم مطلوب' });
    }

    const user = await User.findOne({ username }).select('-password');
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Search user error:', error);
    res.status(500).json({ message: 'خطأ في البحث عن المستخدم' });
  }
});

// تحديث بيانات المستخدم (للأدمن فقط)
app.post('/api/users/update-user', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { username, newUsername, newPassword, newScore, newPearls } = req.body;

    if (!username) {
      return res.status(400).json({ message: 'اسم المستخدم مطلوب' });
    }

    const user = await User.findOne({ username });
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديث البيانات
    if (newUsername && newUsername !== username) {
      // التحقق من عدم وجود اسم المستخدم الجديد
      const existingUser = await User.findOne({ username: newUsername });
      if (existingUser) {
        return res.status(400).json({ message: 'اسم المستخدم الجديد موجود بالفعل' });
      }
      user.username = newUsername;
    }

    if (newPassword) {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      user.password = hashedPassword;
    }

    if (newScore !== undefined && newScore !== null && newScore !== '') {
      user.goldCoins = parseInt(newScore, 10);
    }

    if (newPearls !== undefined && newPearls !== null && newPearls !== '') {
      user.pearls = parseInt(newPearls, 10);
    }

    user.lastActive = new Date();
    await user.save();

    res.json({
      message: 'تم تحديث بيانات المستخدم بنجاح',
      user: {
        id: user._id,
        username: user.username,
        goldCoins: user.goldCoins,
        pearls: user.pearls,
        level: user.level,
        isAdmin: user.isAdmin
      }
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'خطأ في تحديث بيانات المستخدم' });
  }
});

// ========== PROFILE ENDPOINTS ==========

// جلب الأصدقاء
app.get('/api/profile/friends', authenticateToken, async (req, res) => {
  try {
    const friendships = await Friendship.find({
      $or: [
        { requester: req.user.userId, status: 'accepted' },
        { recipient: req.user.userId, status: 'accepted' }
      ]
    }).populate('requester recipient', 'username playerId profileImage');

    const friends = friendships.map(friendship => {
      const friend = friendship.requester._id.toString() === req.user.userId
        ? friendship.recipient
        : friendship.requester;

      return {
        id: friend._id,
        username: friend.username,
        playerId: friend.playerId,
        profileImage: friend.profileImage,
        friendshipId: friendship._id,
        friendsSince: friendship.respondedAt
      };
    });

    res.json(friends);
  } catch (error) {
    console.error('Get friends error:', error);
    res.status(500).json({ message: 'خطأ في جلب الأصدقاء' });
  }
});

// جلب طلبات الصداقة
app.get('/api/profile/friend-requests', authenticateToken, async (req, res) => {
  try {
    // الحصول على Player ID للمستخدم الحالي
    const currentUser = await User.findById(req.user.userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // البحث عن طلبات الصداقة باستخدام Player ID
    const friendRequests = await Friendship.find({
      recipientPlayerId: currentUser.playerId,
      status: 'pending'
    }).populate('requester', 'username playerId profileImage');

    const requests = friendRequests.map(request => ({
      id: request._id,
      requester: {
        id: request.requester._id,
        username: request.requester.username,
        playerId: request.requester.playerId,
        profileImage: request.requester.profileImage
      },
      requestedAt: request.requestedAt
    }));

    console.log(`📥 Found ${requests.length} friend requests for player ${currentUser.playerId}`);
    res.json(requests);
  } catch (error) {
    console.error('Get friend requests error:', error);
    res.status(500).json({ message: 'خطأ في جلب طلبات الصداقة' });
  }
});

// إرسال طلب صداقة
app.post('/api/profile/friend-request', authenticateToken, async (req, res) => {
  try {
    const { friendId } = req.body;

    if (!friendId) {
      return res.status(400).json({ message: 'معرف الصديق مطلوب' });
    }

    // التحقق من وجود المستخدم المستهدف (يمكن أن يكون ObjectId أو Player ID)
    let friend;
    if (mongoose.Types.ObjectId.isValid(friendId)) {
      friend = await User.findById(friendId);
    } else {
      friend = await User.findOne({ playerId: friendId });
    }

    if (!friend) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // الحصول على بيانات المرسل
    const requester = await User.findById(req.user.userId);
    if (!requester) {
      return res.status(404).json({ message: 'خطأ في بيانات المرسل' });
    }

    // التحقق من عدم إرسال طلب لنفسه
    if (friend._id.toString() === req.user.userId) {
      return res.status(400).json({ message: 'لا يمكن إرسال طلب صداقة لنفسك' });
    }

    // التحقق من عدم وجود طلب سابق باستخدام Player IDs
    const existingRequest = await Friendship.findOne({
      $or: [
        { requesterPlayerId: requester.playerId, recipientPlayerId: friend.playerId },
        { requesterPlayerId: friend.playerId, recipientPlayerId: requester.playerId }
      ]
    });

    if (existingRequest) {
      if (existingRequest.status === 'accepted') {
        return res.status(400).json({ message: 'أنتما أصدقاء بالفعل' });
      } else if (existingRequest.status === 'pending') {
        return res.status(400).json({ message: 'طلب الصداقة مرسل بالفعل' });
      }
    }

    // إنشاء طلب صداقة جديد
    const newRequest = new Friendship({
      requester: req.user.userId, // MongoDB ObjectId للتوافق
      requesterPlayerId: requester.playerId, // Player ID الصغير
      recipient: friend._id, // MongoDB ObjectId للتوافق
      recipientPlayerId: friend.playerId, // Player ID الصغير
      status: 'pending'
    });

    await newRequest.save();

    // إنشاء إشعار للمستقبل باستخدام Player ID
    await createNotification(
      friend._id, // MongoDB ObjectId للتوافق
      'friend_request',
      '🤝 طلب صداقة جديد',
      `${requester.username} أرسل لك طلب صداقة`,
      {
        friendshipId: newRequest._id,
        requesterId: req.user.userId,
        requesterName: requester.username,
        requesterPlayerId: requester.playerId,
        recipientPlayerId: friend.playerId
      },
      req.user.userId,
      friend.playerId // Player ID للمستقبل
    );

    console.log(`📤 Friend request sent from ${requester.username} to ${friend.username}`);

    res.json({
      message: 'تم إرسال طلب الصداقة بنجاح',
      request: {
        id: newRequest._id,
        friendId,
        status: 'pending',
        sentAt: newRequest.requestedAt
      }
    });
  } catch (error) {
    console.error('Send friend request error:', error);
    res.status(500).json({ message: 'خطأ في إرسال طلب الصداقة' });
  }
});

// قبول طلب صداقة
app.post('/api/profile/accept-friend', authenticateToken, async (req, res) => {
  try {
    const { friendshipId } = req.body;

    if (!friendshipId) {
      return res.status(400).json({ message: 'معرف طلب الصداقة مطلوب' });
    }

    // البحث عن طلب الصداقة
    const friendship = await Friendship.findById(friendshipId);
    if (!friendship) {
      return res.status(404).json({ message: 'طلب الصداقة غير موجود' });
    }

    // التحقق من أن المستخدم الحالي هو المستقبل
    if (friendship.recipient.toString() !== req.user.userId) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لقبول هذا الطلب' });
    }

    // التحقق من أن الطلب في حالة انتظار
    if (friendship.status !== 'pending') {
      return res.status(400).json({ message: 'طلب الصداقة ليس في حالة انتظار' });
    }

    // تحديث حالة الطلب
    friendship.status = 'accepted';
    friendship.respondedAt = new Date();
    await friendship.save();

    res.json({
      message: 'تم قبول طلب الصداقة بنجاح',
      friendship: {
        id: friendship._id,
        status: 'accepted',
        acceptedAt: friendship.respondedAt
      }
    });
  } catch (error) {
    console.error('Accept friend request error:', error);
    res.status(500).json({ message: 'خطأ في قبول طلب الصداقة' });
  }
});

// فحص حالة الصداقة
app.get('/api/friends/check/:friendId', authenticateToken, async (req, res) => {
  try {
    const { friendId } = req.params;

    if (!friendId) {
      return res.status(400).json({ message: 'معرف الصديق مطلوب' });
    }

    // البحث عن علاقة صداقة مقبولة
    const friendship = await Friendship.findOne({
      $or: [
        { requester: req.user.userId, recipient: friendId, status: 'accepted' },
        { requester: friendId, recipient: req.user.userId, status: 'accepted' }
      ]
    });

    res.json({
      isFriend: !!friendship,
      friendshipId: friendship?._id || null
    });
  } catch (error) {
    console.error('Check friendship error:', error);
    res.status(500).json({ message: 'خطأ في فحص حالة الصداقة' });
  }
});



// البحث عن مستخدم برقم اللاعب
app.get('/api/users/search-by-id/:playerId', authenticateToken, async (req, res) => {
  try {
    const { playerId } = req.params;

    // التحقق من صحة Player ID (يجب أن يكون رقم صحيح)
    if (!playerId || !/^\d+$/.test(playerId)) {
      return res.status(400).json({ message: 'رقم اللاعب يجب أن يكون رقم صحيح' });
    }

    // البحث عن المستخدم برقم اللاعب
    const user = await User.findOne({ playerId });
    if (!user) {
      return res.status(404).json({ message: 'لم يتم العثور على لاعب بهذا الرقم' });
    }

    // التحقق من أن المستخدم لا يبحث عن نفسه
    if (user._id.toString() === req.user.userId) {
      return res.status(400).json({ message: 'لا يمكنك إضافة نفسك كصديق' });
    }

    // إرجاع بيانات المستخدم الأساسية
    res.json({
      id: user._id,
      username: user.username,
      playerId: user.playerId,
      profileImage: user.profileImage,
      level: user.level
    });
  } catch (error) {
    console.error('Search user error:', error);
    res.status(500).json({ message: 'خطأ في البحث عن المستخدم' });
  }
});

// تحويل الذهب إلى لآلئ
app.post('/api/profile/exchange-gold-to-pearls', authenticateToken, async (req, res) => {
  try {
    const { goldAmount } = req.body;
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    if (!goldAmount || goldAmount < 10000) {
      return res.status(400).json({ message: 'الحد الأدنى للتحويل هو 10,000 عملة ذهبية' });
    }

    if (goldAmount % 10000 !== 0) {
      return res.status(400).json({ message: 'يجب أن تكون الكمية مضاعفات 10,000' });
    }

    if (user.goldCoins < goldAmount) {
      return res.status(400).json({ message: 'رصيد العملات الذهبية غير كافي' });
    }

    // حساب عدد اللآلئ (10,000 ذهب = 1 لؤلؤة)
    const pearlsToAdd = goldAmount / 10000;

    // تحديث الرصيد
    user.goldCoins -= goldAmount;
    user.pearls += pearlsToAdd;
    await user.save();

    // حفظ سجل المعاملة
    const transaction = new Transaction({
      user: req.user.userId,
      type: 'exchange',
      amount: -goldAmount,
      currency: 'gold',
      description: `تحويل ${goldAmount} عملة ذهبية إلى ${pearlsToAdd} لؤلؤة`,
      status: 'completed'
    });
    await transaction.save();

    // حفظ سجل اللآلئ المضافة
    const pearlTransaction = new Transaction({
      user: req.user.userId,
      type: 'exchange',
      amount: pearlsToAdd,
      currency: 'pearls',
      description: `استلام ${pearlsToAdd} لؤلؤة من تحويل ${goldAmount} عملة ذهبية`,
      status: 'completed'
    });
    await pearlTransaction.save();

    res.json({
      message: `تم تحويل ${goldAmount} عملة ذهبية إلى ${pearlsToAdd} لؤلؤة بنجاح`,
      newBalance: {
        goldCoins: user.goldCoins,
        pearls: user.pearls
      },
      exchangeDetails: {
        goldSpent: goldAmount,
        pearlsReceived: pearlsToAdd,
        exchangeRate: '10,000 🪙 = 1 🦪'
      }
    });
  } catch (error) {
    console.error('Exchange gold to pearls error:', error);
    res.status(500).json({ message: 'خطأ في تحويل العملات' });
  }
});

// إرسال عنصر
app.post('/api/profile/send-item', authenticateToken, async (req, res) => {
  try {
    const { toUserId, itemType, message } = req.body;
    const fromUser = await User.findById(req.user.userId);

    if (!fromUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    const toUser = await User.findById(toUserId);
    if (!toUser) {
      return res.status(404).json({ message: 'المستخدم المستهدف غير موجود' });
    }

    if (!itemType) {
      return res.status(400).json({ message: 'نوع العنصر مطلوب' });
    }

    // قائمة العناصر المتاحة مع تأثيراتها
    const itemEffects = {
      // العناصر الضارة (تخصم من الذهب فقط)
      'bomb': { type: 'harmful', goldEffect: -2000, pearlsEffect: 0, name: 'قنبلة مدمرة 💣' },
      'bat': { type: 'harmful', goldEffect: -1000, pearlsEffect: 0, name: 'خفاش مؤذي 🦇' },
      'snake': { type: 'harmful', goldEffect: -1500, pearlsEffect: 0, name: 'ثعبان سام 🐍' },

      // العناصر المفيدة (تضيف للرصيد)
      'gem': { type: 'beneficial', goldEffect: 3000, pearlsEffect: 8, name: 'جوهرة نادرة 💎' },
      'star': { type: 'beneficial', goldEffect: 2500, pearlsEffect: 6, name: 'نجمة ذهبية ⭐' },
      'coin': { type: 'beneficial', goldEffect: 1500, pearlsEffect: 4, name: 'عملة خاصة 🪙' },

      // العملات (تضيف للرصيد)
      'gold': { type: 'beneficial', goldEffect: 1000, pearlsEffect: 0, name: 'عملات ذهبية 🪙' }
    };

    if (!itemEffects[itemType]) {
      return res.status(400).json({ message: 'نوع العنصر غير صحيح' });
    }

    const itemEffect = itemEffects[itemType];

    // تطبيق تأثير العنصر على المستقبل
    let effectMessage = '';
    if (itemEffect.goldEffect !== 0) {
      toUser.goldCoins = Math.max(0, toUser.goldCoins + itemEffect.goldEffect);
      if (itemEffect.goldEffect > 0) {
        effectMessage += `+${itemEffect.goldEffect} عملة ذهبية `;
      } else {
        effectMessage += `${itemEffect.goldEffect} عملة ذهبية `;
      }
    }

    if (itemEffect.pearlsEffect !== 0) {
      toUser.pearls = Math.max(0, toUser.pearls + itemEffect.pearlsEffect);
      if (itemEffect.pearlsEffect > 0) {
        effectMessage += `+${itemEffect.pearlsEffect} لؤلؤة `;
      } else {
        effectMessage += `${itemEffect.pearlsEffect} لؤلؤة `;
      }
    }

    await toUser.save();

    // حفظ سجل إرسال العنصر
    const itemGift = new Gift({
      sender: req.user.userId,
      recipient: toUserId,
      giftType: 'item',
      amount: 1,
      message: message || `عنصر ${itemType}`,
      itemType: itemType,
      status: 'sent'
    });
    await itemGift.save();

    // حفظ سجل المعاملة للمرسل
    const senderTransaction = new Transaction({
      user: req.user.userId,
      type: 'gift_sent',
      amount: -1,
      currency: 'gold', // مؤقت
      description: `إرسال عنصر ${itemType} إلى ${toUser.username}`,
      relatedUser: toUserId
    });

    // حفظ سجل المعاملة للمستقبل
    const recipientTransaction = new Transaction({
      user: toUserId,
      type: 'gift_received',
      amount: 1,
      currency: 'gold', // مؤقت
      description: `استلام عنصر ${itemType} من ${fromUser.username}`,
      relatedUser: req.user.userId
    });

    await senderTransaction.save();
    await recipientTransaction.save();

    // إنشاء إشعار للمستقبل مع التأثير
    const notificationTitle = itemEffect.type === 'harmful' ? '⚠️ تأثير ضار!' : '🎁 عنصر مفيد!';
    const notificationMessage = `${fromUser.username} أرسل لك ${itemEffect.name}${effectMessage ? ` (${effectMessage.trim()})` : ''}`;

    await createNotification(
      toUserId,
      'item_received',
      notificationTitle,
      notificationMessage,
      {
        itemType,
        itemGiftId: itemGift._id,
        effect: effectMessage.trim(),
        newBalance: {
          goldCoins: toUser.goldCoins,
          pearls: toUser.pearls
        }
      },
      req.user.userId
    );

    res.json({
      message: `تم إرسال ${itemEffect.name} إلى ${toUser.username} بنجاح${effectMessage ? ` (${effectMessage.trim()})` : ''}`,
      itemGift: {
        id: itemGift._id,
        itemType,
        itemName: itemEffect.name,
        recipient: toUser.username,
        effect: effectMessage.trim(),
        recipientNewBalance: {
          goldCoins: toUser.goldCoins,
          pearls: toUser.pearls
        },
        sentAt: itemGift.createdAt
      }
    });
  } catch (error) {
    console.error('Send item error:', error);
    res.status(500).json({ message: 'خطأ في إرسال العنصر' });
  }
});

// جلب الإشعارات
app.get('/api/notifications', authenticateToken, async (req, res) => {
  try {
    // الحصول على Player ID للمستخدم الحالي
    const currentUser = await User.findById(req.user.userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // البحث عن الإشعارات باستخدام Player ID
    const notifications = await Notification.find({ userPlayerId: currentUser.playerId })
      .populate('fromUser', 'username profileImage playerId')
      .sort({ createdAt: -1 })
      .limit(50);

    console.log(`📬 Found ${notifications.length} notifications for player ${currentUser.playerId}`);
    res.json(notifications);
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ message: 'خطأ في جلب الإشعارات' });
  }
});

// تحديد إشعار واحد كمقروء
app.put('/api/notifications/:id/read', authenticateToken, async (req, res) => {
  try {
    const notification = await Notification.findOneAndUpdate(
      { _id: req.params.id, user: req.user.userId },
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    res.json({ message: 'تم تحديث الإشعار كمقروء', notification });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'خطأ في تحديث الإشعار' });
  }
});

// تحديد جميع الإشعارات كمقروءة
app.put('/api/notifications/mark-all-read', authenticateToken, async (req, res) => {
  try {
    // الحصول على Player ID للمستخدم الحالي
    const currentUser = await User.findById(req.user.userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديث الإشعارات باستخدام Player ID
    await Notification.updateMany(
      { userPlayerId: currentUser.playerId },
      { isRead: true }
    );

    console.log(`📖 Marked all notifications as read for player ${currentUser.playerId}`);
    res.json({ message: 'تم تحديث جميع الإشعارات كمقروءة' });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ message: 'خطأ في تحديث الإشعارات' });
  }
});

// تحديد الإشعارات كمقروءة (API القديم للتوافق)
app.put('/api/notifications/mark-read', authenticateToken, async (req, res) => {
  try {
    const { notificationIds } = req.body;

    await Notification.updateMany(
      {
        _id: { $in: notificationIds },
        user: req.user.userId
      },
      { isRead: true }
    );

    res.json({ message: 'تم تحديد الإشعارات كمقروءة' });
  } catch (error) {
    console.error('Mark notifications read error:', error);
    res.status(500).json({ message: 'خطأ في تحديث الإشعارات' });
  }
});

// حذف المحادثات القديمة يدوياً (للمشرفين)
app.delete('/api/messages/cleanup', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم مشرف
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ message: 'غير مصرح لك بهذا الإجراء' });
    }

    const { days = 3 } = req.body;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const result = await Message.deleteMany({
      createdAt: { $lt: cutoffDate }
    });

    res.json({
      message: `تم حذف ${result.deletedCount} رسالة أقدم من ${days} أيام`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Manual cleanup error:', error);
    res.status(500).json({ message: 'خطأ في حذف المحادثات' });
  }
});

// حذف محادثة محددة بين مستخدمين
app.delete('/api/messages/conversation/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user.userId;

    const result = await Message.deleteMany({
      $or: [
        { sender: currentUserId, recipient: userId },
        { sender: userId, recipient: currentUserId }
      ]
    });

    res.json({
      message: `تم حذف ${result.deletedCount} رسالة من المحادثة`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Delete conversation error:', error);
    res.status(500).json({ message: 'خطأ في حذف المحادثة' });
  }
});

// جلب المحادثات
app.get('/api/messages/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // الحصول على Player ID للمستخدم الحالي
    const currentUser = await User.findById(req.user.userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديد ما إذا كان userId هو ObjectId أم Player ID
    let targetUser;
    if (mongoose.Types.ObjectId.isValid(userId)) {
      targetUser = await User.findById(userId);
    } else {
      targetUser = await User.findOne({ playerId: userId });
    }

    if (!targetUser) {
      return res.status(404).json({ message: 'المستخدم المستهدف غير موجود' });
    }

    // البحث عن الرسائل باستخدام Player IDs
    const messages = await Message.find({
      $or: [
        { senderPlayerId: currentUser.playerId, recipientPlayerId: targetUser.playerId },
        { senderPlayerId: targetUser.playerId, recipientPlayerId: currentUser.playerId }
      ]
    })
    .populate('sender', 'username profileImage playerId')
    .populate('recipient', 'username profileImage playerId')
    .sort({ createdAt: 1 });

    console.log(`💬 Found ${messages.length} messages between ${currentUser.playerId} and ${targetUser.playerId}`);
    res.json(messages);
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ message: 'خطأ في جلب الرسائل' });
  }
});

// إرسال رسالة
app.post('/api/messages', authenticateToken, async (req, res) => {
  try {
    const { recipientId, content, messageType = 'text' } = req.body;

    if (!recipientId || !content) {
      return res.status(400).json({ message: 'معرف المستقبل والمحتوى مطلوبان' });
    }

    // الحصول على بيانات المرسل
    const sender = await User.findById(req.user.userId);
    if (!sender) {
      return res.status(404).json({ message: 'خطأ في بيانات المرسل' });
    }

    // تحديد ما إذا كان recipientId هو ObjectId أم Player ID
    let recipient;
    if (mongoose.Types.ObjectId.isValid(recipientId)) {
      recipient = await User.findById(recipientId);
    } else {
      recipient = await User.findOne({ playerId: recipientId });
    }

    if (!recipient) {
      return res.status(404).json({ message: 'المستخدم المستهدف غير موجود' });
    }

    const message = new Message({
      sender: req.user.userId, // MongoDB ObjectId للتوافق
      senderPlayerId: sender.playerId, // Player ID الصغير
      recipient: recipient._id, // MongoDB ObjectId للتوافق
      recipientPlayerId: recipient.playerId, // Player ID الصغير
      content,
      messageType
    });
    await message.save();

    // لا نرسل إشعار للرسائل العادية - المحادثة متزامنة

    const populatedMessage = await Message.findById(message._id)
      .populate('sender', 'username profileImage playerId')
      .populate('recipient', 'username profileImage playerId');

    // WebSocket سيتم إرساله من العميل بعد نجاح الحفظ

    res.json({
      message: 'تم إرسال الرسالة بنجاح',
      messageData: populatedMessage
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ message: 'خطأ في إرسال الرسالة' });
  }
});

// جلب الهدايا
app.get('/api/profile/gifts', authenticateToken, async (req, res) => {
  try {
    // في المستقبل يمكن إضافة نظام الهدايا من قاعدة البيانات
    // حالياً نرجع قائمة فارغة
    res.json([]);
  } catch (error) {
    console.error('Get gifts error:', error);
    res.status(500).json({ message: 'خطأ في جلب الهدايا' });
  }
});

// إرسال هدية
app.post('/api/profile/send-gift', authenticateToken, async (req, res) => {
  try {
    const { toUserId, giftType, amount, message } = req.body;
    const fromUser = await User.findById(req.user.userId);

    if (!fromUser) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    const toUser = await User.findById(toUserId);
    if (!toUser) {
      return res.status(404).json({ message: 'المستخدم المستهدف غير موجود' });
    }

    // التحقق من الرصيد
    if (giftType === 'gold' && fromUser.goldCoins < amount) {
      return res.status(400).json({ message: 'رصيد العملات الذهبية غير كافي' });
    }

    if (giftType === 'pearls' && fromUser.pearls < amount) {
      return res.status(400).json({ message: 'رصيد اللآلئ غير كافي' });
    }

    // تحويل الهدية
    if (giftType === 'gold') {
      fromUser.goldCoins -= amount;
      toUser.goldCoins += amount;
    } else if (giftType === 'pearls') {
      fromUser.pearls -= amount;
      toUser.pearls += amount;
    }

    await fromUser.save();
    await toUser.save();

    // حفظ سجل الهدية في قاعدة البيانات
    const gift = new Gift({
      sender: req.user.userId,
      recipient: toUserId,
      giftType,
      amount,
      message,
      status: 'sent'
    });
    await gift.save();

    // حفظ سجل المعاملات
    const senderTransaction = new Transaction({
      user: req.user.userId,
      type: 'gift_sent',
      amount: -amount,
      currency: giftType,
      description: `هدية مرسلة إلى ${toUser.username}`,
      relatedUser: toUserId
    });

    const recipientTransaction = new Transaction({
      user: toUserId,
      type: 'gift_received',
      amount: amount,
      currency: giftType,
      description: `هدية مستلمة من ${fromUser.username}`,
      relatedUser: req.user.userId
    });

    await senderTransaction.save();
    await recipientTransaction.save();

    // إنشاء إشعار للمستقبل
    await createNotification(
      toUserId,
      'gift_received',
      '🎁 هدية جديدة!',
      `استلمت ${amount} ${giftType === 'gold' ? 'عملة ذهبية' : 'لؤلؤة'} من ${fromUser.username}`,
      {
        giftType,
        amount,
        giftId: gift._id
      },
      req.user.userId
    );

    res.json({
      message: `تم إرسال ${amount} ${giftType === 'gold' ? 'عملة ذهبية' : 'لؤلؤة'} إلى ${toUser.username}`,
      fromUserBalance: {
        goldCoins: fromUser.goldCoins,
        pearls: fromUser.pearls
      },
      giftId: gift._id
    });
  } catch (error) {
    console.error('Send gift error:', error);
    res.status(500).json({ message: 'خطأ في إرسال الهدية' });
  }
});

// استلام هدية
app.post('/api/profile/claim-gift', authenticateToken, async (req, res) => {
  try {
    const { giftId } = req.body;

    if (!giftId) {
      return res.status(400).json({ message: 'معرف الهدية مطلوب' });
    }

    // البحث عن الهدية
    const gift = await Gift.findById(giftId);
    if (!gift) {
      return res.status(404).json({ message: 'الهدية غير موجودة' });
    }

    // التحقق من أن المستخدم الحالي هو المستقبل
    if (gift.recipient.toString() !== req.user.userId) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لاستلام هذه الهدية' });
    }

    // التحقق من أن الهدية لم تُستلم بعد
    if (gift.status === 'received') {
      return res.status(400).json({ message: 'تم استلام هذه الهدية بالفعل' });
    }

    // تحديث حالة الهدية
    gift.status = 'received';
    await gift.save();

    res.json({
      message: 'تم استلام الهدية بنجاح',
      gift: {
        id: gift._id,
        giftType: gift.giftType,
        amount: gift.amount,
        message: gift.message,
        status: 'received'
      }
    });
  } catch (error) {
    console.error('Claim gift error:', error);
    res.status(500).json({ message: 'خطأ في استلام الهدية' });
  }
});

// جلب العناصر والدروع
app.get('/api/profile/items', authenticateToken, async (req, res) => {
  try {
    // في المستقبل يمكن إضافة نظام العناصر
    // حالياً نرجع قائمة فارغة
    res.json([]);
  } catch (error) {
    console.error('Get items error:', error);
    res.status(500).json({ message: 'خطأ في جلب العناصر' });
  }
});

// جلب عناصر مستخدم محدد (للمكون المحمول)
app.get('/api/user-items/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // في المستقبل: جلب العناصر الفعلية من قاعدة البيانات
    // حالياً: إرجاع عناصر افتراضية للاختبار
    const defaultItems = {
      gems: Math.floor(Math.random() * 10),
      stars: Math.floor(Math.random() * 15),
      coins: Math.floor(Math.random() * 20),
      bombs: Math.floor(Math.random() * 5),
      bats: Math.floor(Math.random() * 8),
      snakes: Math.floor(Math.random() * 3)
    };

    res.json({
      success: true,
      items: defaultItems
    });
  } catch (error) {
    console.error('Get user items error:', error);
    res.status(500).json({ message: 'خطأ في جلب عناصر المستخدم' });
  }
});

// جلب حالة الدرع للمستخدم
app.get('/api/profile/shield/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // جلب الدرع النشط من قاعدة البيانات
    const activeShield = await Shield.findOne({
      user: userId,
      isActive: true,
      expiresAt: { $gt: new Date() }
    });

    if (activeShield) {
      const remainingTime = activeShield.expiresAt.getTime() - Date.now();
      res.json({
        shield: {
          id: activeShield._id,
          isActive: true,
          type: activeShield.type,
          expiresAt: activeShield.expiresAt,
          remainingTime: Math.max(0, remainingTime),
          activatedAt: activeShield.activatedAt
        }
      });
    } else {
      res.json({
        shield: {
          isActive: false,
          type: null,
          expiresAt: null,
          remainingTime: 0
        }
      });
    }
  } catch (error) {
    console.error('Get shield error:', error);
    res.status(500).json({ message: 'خطأ في جلب معلومات الدرع' });
  }
});

// تفعيل الدرع الواقي
app.post('/api/profile/activate-shield', authenticateToken, async (req, res) => {
  try {
    const { shieldType } = req.body; // 'gold' or 'usd'
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديد تكلفة الدرع
    const costs = {
      gold: 5000,  // 5000 عملة ذهبية
      usd: 1       // 1 دولار (يحتاج نظام دفع)
    };

    if (shieldType === 'gold') {
      if (user.goldCoins < costs.gold) {
        return res.status(400).json({
          message: `تحتاج إلى ${costs.gold} عملة ذهبية لتفعيل الدرع الواقي`
        });
      }

      // خصم العملات
      user.goldCoins -= costs.gold;
      await user.save();

      // حفظ الدرع في قاعدة البيانات
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ساعة
      const shield = new Shield({
        user: req.user.userId,
        type: 'gold',
        isActive: true,
        expiresAt,
        cost: costs.gold,
        currency: 'gold'
      });
      await shield.save();

      // حفظ سجل المعاملة
      const transaction = new Transaction({
        user: req.user.userId,
        type: 'shield_purchase',
        amount: -costs.gold,
        currency: 'gold',
        description: 'شراء درع واقي لمدة 24 ساعة',
        status: 'completed'
      });
      await transaction.save();

      res.json({
        message: 'تم تفعيل الدرع الواقي لمدة 24 ساعة',
        shield: {
          id: shield._id,
          isActive: true,
          type: 'gold',
          expiresAt,
          remainingTime: 24 * 60 * 60 * 1000
        },
        newBalance: user.goldCoins
      });
    } else if (shieldType === 'usd') {
      // في المستقبل: تكامل مع نظام الدفع
      res.status(501).json({
        message: 'الدفع بالدولار غير متاح حالياً. استخدم العملات الذهبية.'
      });
    } else {
      res.status(400).json({ message: 'نوع الدرع غير صحيح' });
    }
  } catch (error) {
    console.error('Activate shield error:', error);
    res.status(500).json({ message: 'خطأ في تفعيل الدرع الواقي' });
  }
});

// جلب المعاملات
app.get('/api/profile/transactions', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    const transactions = await Transaction.find({ user: req.user.userId })
      .populate('relatedUser', 'username playerId')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Transaction.countDocuments({ user: req.user.userId });

    const formattedTransactions = transactions.map(transaction => ({
      id: transaction._id,
      type: transaction.type,
      amount: transaction.amount,
      currency: transaction.currency,
      description: transaction.description,
      status: transaction.status,
      relatedUser: transaction.relatedUser ? {
        id: transaction.relatedUser._id,
        username: transaction.relatedUser.username,
        playerId: transaction.relatedUser.playerId
      } : null,
      createdAt: transaction.createdAt
    }));

    res.json({
      transactions: formattedTransactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get transactions error:', error);
    res.status(500).json({ message: 'خطأ في جلب المعاملات' });
  }
});

// شحن الرصيد
app.post('/api/profile/charge-balance', authenticateToken, async (req, res) => {
  try {
    const { amount } = req.body;
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'مبلغ الشحن غير صحيح' });
    }

    // في المستقبل: تكامل مع نظام الدفع الحقيقي
    // حالياً: محاكاة شحن مجاني للاختبار
    user.goldCoins += amount;
    await user.save();

    // حفظ سجل المعاملة في قاعدة البيانات
    const transaction = new Transaction({
      user: req.user.userId,
      type: 'charge',
      amount,
      currency: 'gold',
      description: `شحن رصيد - ${amount} عملة ذهبية`,
      status: 'completed'
    });
    await transaction.save();

    res.json({
      message: `تم شحن ${amount} عملة ذهبية بنجاح`,
      newBalance: user.goldCoins,
      transaction: {
        id: transaction._id,
        type: 'charge',
        amount,
        timestamp: transaction.createdAt,
        status: 'completed'
      }
    });
  } catch (error) {
    console.error('Charge balance error:', error);
    res.status(500).json({ message: 'خطأ في شحن الرصيد' });
  }
});

// تفعيل عنصر
app.post('/api/profile/activate-item', authenticateToken, async (req, res) => {
  try {
    const { itemId } = req.body;

    // في المستقبل: جلب العنصر من قاعدة البيانات وتفعيله
    // حالياً: محاكاة تفعيل العنصر

    res.json({
      message: 'تم تفعيل العنصر بنجاح',
      item: {
        id: itemId,
        isActive: true,
        activatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Activate item error:', error);
    res.status(500).json({ message: 'خطأ في تفعيل العنصر' });
  }
});

// ========== GAME SETTINGS ENDPOINTS ==========

// جلب إعدادات اللعبة
app.get('/api/game/settings', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    // إعدادات افتراضية للعبة
    const defaultSettings = {
      boxCount: 20,
      winRate: 0.3,
      gameSpeed: 1,
      difficulty: 'medium'
    };

    res.json(defaultSettings);
  } catch (error) {
    console.error('Get game settings error:', error);
    res.status(500).json({ message: 'خطأ في جلب إعدادات اللعبة' });
  }
});

// تحديث إعدادات اللعبة
app.post('/api/game/settings', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { boxCount, winRate, gameSpeed, difficulty } = req.body;

    // في المستقبل يمكن حفظ هذه الإعدادات في قاعدة البيانات
    // حالياً نرجع رسالة نجاح
    res.json({
      message: 'تم تحديث إعدادات اللعبة بنجاح',
      settings: { boxCount, winRate, gameSpeed, difficulty }
    });
  } catch (error) {
    console.error('Update game settings error:', error);
    res.status(500).json({ message: 'خطأ في تحديث إعدادات اللعبة' });
  }
});

// ========== ADMIN ANALYTICS ENDPOINTS ==========

// جلب النشاطات المشبوهة
app.get('/api/admin/suspicious-activities', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    // في المستقبل يمكن إضافة منطق لرصد النشاطات المشبوهة
    // حالياً نرجع قائمة فارغة
    res.json([]);
  } catch (error) {
    console.error('Get suspicious activities error:', error);
    res.status(500).json({ message: 'خطأ في جلب النشاطات المشبوهة' });
  }
});

// جلب معرف اللاعب
app.get('/api/admin/users/:userId/player-id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { userId } = req.params;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({ playerId: user.playerId });
  } catch (error) {
    console.error('Get player ID error:', error);
    res.status(500).json({ message: 'خطأ في جلب معرف اللاعب' });
  }
});

// جلب جميع المستخدمين مع الصور (للأدمن)
app.get('/api/users/admin/users-with-ids', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const search = req.query.search || '';
    const skip = (page - 1) * limit;

    // بناء استعلام البحث
    let query = {};
    if (search) {
      query = {
        $or: [
          { username: { $regex: search, $options: 'i' } },
          { playerId: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // جلب المستخدمين مع التصفح
    const users = await User.find(query)
      .select('_id username playerId profileImage goldCoins pearls level isAdmin lastActive')
      .sort({ lastActive: -1 })
      .skip(skip)
      .limit(limit);

    // عدد المستخدمين الإجمالي
    const totalUsers = await User.countDocuments(query);
    const totalPages = Math.ceil(totalUsers / limit);

    res.json({
      users: users.map(user => ({
        id: user._id,
        userId: user._id, // إضافة userId للتوافق مع الواجهة الأمامية
        username: user.username,
        playerId: user.playerId,
        profileImage: user.profileImage,
        goldCoins: user.goldCoins,
        pearls: user.pearls,
        level: user.level,
        isAdmin: user.isAdmin,
        lastActive: user.lastActive
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalUsers,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Get users with IDs error:', error);
    res.status(500).json({ message: 'خطأ في جلب المستخدمين' });
  }
});

// تحديث معرف اللاعب (للأدمن فقط)
app.put('/api/admin/users/:userId/player-id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { userId } = req.params;
    const { playerId } = req.body;

    // التحقق من صحة userId (يجب أن يكون ObjectId صحيح)
    if (!userId || userId.length < 12) {
      console.log(`❌ Invalid userId format: ${userId}`);
      return res.status(400).json({ message: 'معرف المستخدم غير صحيح' });
    }

    // التحقق من أن userId هو ObjectId صحيح
    if (!/^[0-9a-fA-F]{24}$/.test(userId)) {
      console.log(`❌ Invalid ObjectId format: ${userId}`);
      return res.status(400).json({ message: 'معرف المستخدم غير صحيح - يجب أن يكون ObjectId صحيح' });
    }

    // التحقق من صحة Player ID الجديد (يجب أن يكون رقم صحيح من 1 إلى ما لا نهاية)
    if (!playerId || !/^\d+$/.test(playerId) || parseInt(playerId) < 1) {
      return res.status(400).json({ message: 'Player ID يجب أن يكون رقم صحيح أكبر من أو يساوي 1' });
    }

    console.log(`🔍 Looking for user with ObjectId: ${userId}`);
    const user = await User.findById(userId);
    if (!user) {
      console.log(`❌ User not found with ObjectId: ${userId}`);
      return res.status(404).json({ message: 'المستخدم غير موجود في قاعدة البيانات الحالية' });
    }

    // التحقق من عدم وجود معرف اللاعب مع مستخدم آخر
    const existingUser = await User.findOne({ playerId, _id: { $ne: userId } });
    if (existingUser) {
      return res.status(400).json({
        message: `معرف اللاعب ${playerId} موجود بالفعل مع المستخدم: ${existingUser.username}`
      });
    }

    // حفظ Player ID القديم للسجل
    const oldPlayerId = user.playerId;

    // تحديث معرف اللاعب
    user.playerId = playerId;
    await user.save();

    console.log(`✅ Updated playerId for user: ${user.username} (${userId}) -> ${oldPlayerId} => ${playerId}`);
    res.json({
      message: `تم تحديث معرف اللاعب بنجاح من ${oldPlayerId} إلى ${playerId}`,
      user: {
        id: user._id,
        username: user.username,
        oldPlayerId: oldPlayerId,
        newPlayerId: user.playerId
      }
    });
  } catch (error) {
    console.error('Update player ID error:', error);

    // إذا كان الخطأ متعلق بـ ObjectId
    if (error.name === 'CastError' && error.kind === 'ObjectId') {
      return res.status(400).json({
        message: 'معرف المستخدم غير صحيح - لا يمكن العثور على هذا المستخدم في قاعدة البيانات الحالية',
        details: `المعرف المطلوب: ${req.params.userId} غير موجود`
      });
    }

    res.status(500).json({ message: 'خطأ في تحديث معرف اللاعب' });
  }
});

// تحديث بيانات المستخدم (للأدمن فقط) - endpoint جديد
app.put('/api/users/admin/update/:userId', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { userId } = req.params;
    const updates = req.body;

    // التحقق من صحة userId
    if (!userId || userId === 'undefined' || userId === 'null') {
      console.error('❌ Invalid userId in update request:', userId);
      return res.status(400).json({
        message: 'معرف المستخدم غير صحيح',
        details: `المعرف المرسل: ${userId}`
      });
    }

    // التحقق من صحة ObjectId
    if (!/^[0-9a-fA-F]{24}$/.test(userId)) {
      console.error('❌ Invalid ObjectId format:', userId);
      return res.status(400).json({
        message: 'معرف المستخدم غير صحيح - يجب أن يكون ObjectId صحيح',
        details: `المعرف المرسل: ${userId}`
      });
    }

    console.log(`🔄 Updating user ${userId} with:`, updates);
    const user = await User.findById(userId);
    if (!user) {
      console.error('❌ User not found:', userId);
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // تحديث البيانات المسموحة
    const allowedUpdates = ['username', 'email', 'goldCoins', 'pearls', 'coins', 'level', 'isAdmin'];
    const updateData = {};

    for (const key of allowedUpdates) {
      if (updates[key] !== undefined) {
        updateData[key] = updates[key];
      }
    }

    // التحقق من اسم المستخدم الجديد إذا تم تغييره
    if (updateData.username && updateData.username !== user.username) {
      const existingUser = await User.findOne({ username: updateData.username });
      if (existingUser) {
        return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
      }
    }

    // تطبيق التحديثات
    Object.assign(user, updateData);
    await user.save();

    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        goldCoins: user.goldCoins,
        pearls: user.pearls,
        coins: user.coins,
        level: user.level,
        isAdmin: user.isAdmin,
        playerId: user.playerId
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'خطأ في تحديث المستخدم' });
  }
});

// عرض جميع المستخدمين مع معرفاتهم (للتشخيص)
app.get('/api/admin/debug/all-users', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const users = await User.find({}).select('_id username playerId email createdAt');

    console.log(`📊 Total users in database: ${users.length}`);
    users.forEach(user => {
      console.log(`👤 User: ${user.username} | ObjectId: ${user._id} | PlayerId: ${user.playerId || 'N/A'}`);
    });

    res.json({
      message: 'جميع المستخدمين في قاعدة البيانات الحالية',
      totalUsers: users.length,
      users: users.map(user => ({
        objectId: user._id,
        username: user.username,
        playerId: user.playerId || 'N/A',
        email: user.email,
        createdAt: user.createdAt
      }))
    });
  } catch (error) {
    console.error('Debug users error:', error);
    res.status(500).json({ message: 'خطأ في جلب المستخدمين' });
  }
});

// حذف مستخدم (للأدمن فقط)
app.delete('/api/users/admin/delete/:userId', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { userId } = req.params;

    // التحقق من صحة ObjectId
    if (!/^[0-9a-fA-F]{24}$/.test(userId)) {
      return res.status(400).json({ message: 'معرف المستخدم غير صحيح' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // منع حذف المشرف الحالي
    if (user._id.toString() === req.user.userId) {
      return res.status(400).json({ message: 'لا يمكن حذف حسابك الخاص' });
    }

    // حذف المستخدم
    await User.findByIdAndDelete(userId);

    console.log(`🗑️ Deleted user: ${user.username} (${userId})`);
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'خطأ في حذف المستخدم' });
  }
});

// حذف صورة مستخدم (للأدمن)
app.delete('/api/users/admin/delete-image/:userId', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { userId } = req.params;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // حذف الصورة
    user.profileImage = null;
    await user.save();

    res.json({ message: 'تم حذف الصورة بنجاح' });
  } catch (error) {
    console.error('Delete user image error:', error);
    res.status(500).json({ message: 'خطأ في حذف الصورة' });
  }
});

// إدارة صورة المستخدم (للأدمن) - endpoint عام
app.put('/api/users/admin/manage-user-image', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات الأدمن
    const admin = await User.findById(req.user.userId);
    if (!admin || !admin.isAdmin) {
      return res.status(403).json({ message: 'صلاحيات المشرف مطلوبة' });
    }

    const { targetUserId, action, imageData, imageType } = req.body;

    console.log('🖼️ Image management request:', {
      targetUserId,
      action,
      hasImageData: !!imageData,
      imageType
    });

    if (!targetUserId || !action) {
      console.error('❌ Missing required fields:', { targetUserId, action });
      return res.status(400).json({ message: 'معرف المستخدم والإجراء مطلوبان' });
    }

    // التحقق من صحة ObjectId
    if (!/^[0-9a-fA-F]{24}$/.test(targetUserId)) {
      console.error('❌ Invalid ObjectId format:', targetUserId);
      return res.status(400).json({
        message: 'معرف المستخدم غير صحيح',
        details: `المعرف المرسل: ${targetUserId}`
      });
    }

    console.log('🔍 Looking for user with ID:', targetUserId);
    const user = await User.findById(targetUserId);
    if (!user) {
      console.error('❌ User not found:', targetUserId);
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    console.log('✅ Found user:', user.username);

    switch (action) {
      case 'delete':
      case 'remove_avatar':
      case 'remove_profile_image':
        console.log('🗑️ Removing image for user:', user.username);
        user.profileImage = null;
        await user.save();
        console.log('✅ Image removed successfully');
        res.json({ message: 'تم حذف الصورة بنجاح' });
        break;

      case 'update':
      case 'change_avatar':
      case 'change_profile_image':
        if (imageData) {
          console.log('📤 Updating image for user:', user.username);
          console.log('📊 Image data size:', imageData.length, 'characters');
          user.profileImage = imageData;
          await user.save();
          console.log('✅ Image updated successfully');
          res.json({ message: 'تم تحديث الصورة بنجاح' });
        } else {
          console.error('❌ No image data provided for update action');
          res.status(400).json({ message: 'بيانات الصورة مطلوبة' });
        }
        break;

      default:
        console.error('❌ Invalid action:', action);
        res.status(400).json({ message: `إجراء غير صحيح: ${action}` });
    }
  } catch (error) {
    console.error('Manage user image error:', error);
    res.status(500).json({ message: 'خطأ في إدارة الصورة' });
  }
});

// ========== GAME ECONOMY ENDPOINTS ==========

// تحديث رصيد اللاعب
app.post('/api/users/update-balance', authenticateToken, async (req, res) => {
  try {
    const { balanceChange, gameType, sessionId, gameResult } = req.body;
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // التحقق من صحة التغيير
    const newBalance = (user.goldCoins || 0) + balanceChange;
    if (newBalance < 0) {
      return res.status(400).json({ message: 'رصيد غير كافي' });
    }

    // تحديث الرصيد
    user.goldCoins = newBalance;
    await user.save();

    // حفظ إحصائيات اللعبة
    const gameStats = new GameStats({
      userId: req.user.userId,
      gameType: gameType,
      sessionId: sessionId,
      startTime: new Date(),
      betAmount: gameResult.lossAmount || 0,
      winAmount: gameResult.winAmount || 0,
      lossAmount: gameResult.lossAmount || 0,
      netResult: balanceChange,
      playerScore: gameResult.playerScore || 0,
      skillFactor: gameResult.skillFactor || 0,
      economicFactor: gameResult.economicFactor || 0,
      winProbability: gameResult.probability || 0
    });

    await gameStats.save();

    res.json({
      success: true,
      newBalance: newBalance,
      change: balanceChange
    });

  } catch (error) {
    console.error('خطأ في تحديث الرصيد:', error);
    res.status(500).json({ message: 'خطأ في تحديث الرصيد' });
  }
});

// جلب بيانات الملف الشخصي للألعاب
app.get('/api/users/profile', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({
      id: user._id,
      username: user.username,
      playerId: user.playerId,
      coins: user.goldCoins || 0,
      pearls: user.pearls || 0,
      profileImage: user.profileImage,
      level: user.level || 1,
      experience: user.experience || 0
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات الملف الشخصي:', error);
    res.status(500).json({ message: 'خطأ في جلب بيانات الملف الشخصي' });
  }
});



// إنهاء جلسة اللعب
app.post('/api/games/session-end', authenticateToken, async (req, res) => {
  try {
    const sessionData = req.body;

    // تحديث إحصائيات الجلسة
    await GameStats.findOneAndUpdate(
      { sessionId: sessionData.sessionId, userId: req.user.userId },
      {
        endTime: new Date(sessionData.endTime),
        duration: sessionData.duration,
        gamesPlayed: sessionData.gamesPlayed,
        netResult: sessionData.netResult
      },
      { upsert: true }
    );

    res.json({ success: true });

  } catch (error) {
    console.error('خطأ في إنهاء الجلسة:', error);
    res.status(500).json({ message: 'خطأ في إنهاء الجلسة' });
  }
});

// جلب إحصائيات اللاعب
app.get('/api/games/player-stats', authenticateToken, async (req, res) => {
  try {
    const stats = await GameStats.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(req.user.userId) } },
      {
        $group: {
          _id: '$gameType',
          totalGames: { $sum: '$gamesPlayed' },
          totalWinnings: { $sum: '$winAmount' },
          totalLosses: { $sum: '$lossAmount' },
          netResult: { $sum: '$netResult' },
          avgSkillFactor: { $avg: '$skillFactor' },
          avgWinProbability: { $avg: '$winProbability' }
        }
      }
    ]);

    const overallStats = await GameStats.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(req.user.userId) } },
      {
        $group: {
          _id: null,
          totalGames: { $sum: '$gamesPlayed' },
          totalWinnings: { $sum: '$winAmount' },
          totalLosses: { $sum: '$lossAmount' },
          netResult: { $sum: '$netResult' },
          totalSessions: { $sum: 1 }
        }
      }
    ]);

    res.json({
      gameStats: stats,
      overallStats: overallStats[0] || {
        totalGames: 0,
        totalWinnings: 0,
        totalLosses: 0,
        netResult: 0,
        totalSessions: 0
      }
    });

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.status(500).json({ message: 'خطأ في جلب الإحصائيات' });
  }
});

// ========== VOICE ROOMS ENDPOINTS ==========

// إنشاء غرف شخصية للمستخدمين الموجودين الذين لا يملكون غرف
app.post('/api/voice-rooms/create-missing-rooms', async (req, res) => {
  try {
    // جلب جميع المستخدمين
    const allUsers = await User.find({});
    let createdRooms = 0;
    let errors = [];

    for (const user of allUsers) {
      try {
        // التحقق من وجود غرفة للمستخدم
        const existingRoom = await VoiceRoom.findOne({ ownerId: user._id });

        if (!existingRoom) {
          // إنشاء غرفة جديدة للمستخدم
          const personalRoom = new VoiceRoom({
            name: `غرفة ${user.username}`,
            ownerId: user._id, // MongoDB ObjectId للتوافق
            ownerPlayerId: user.playerId, // Player ID الصغير
            ownerName: user.username,
            participants: [],
            maxParticipants: 10,
            isActive: true
          });

          await personalRoom.save();
          createdRooms++;
          console.log(`🎤 تم إنشاء غرفة صوتية للمستخدم: ${user.username} (Player ID: ${user.playerId})`);
        }
      } catch (userError) {
        errors.push(`خطأ في إنشاء غرفة للمستخدم ${user.username}: ${userError.message}`);
        console.error(`❌ خطأ في إنشاء غرفة للمستخدم ${user.username}:`, userError);
      }
    }

    res.json({
      message: `تم إنشاء ${createdRooms} غرفة صوتية جديدة`,
      createdRooms,
      totalUsers: allUsers.length,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الغرف المفقودة:', error);
    res.status(500).json({ error: 'خطأ في إنشاء الغرف المفقودة' });
  }
});

// جلب قائمة الغرف الصوتية الشخصية
app.get('/api/voice-rooms', async (req, res) => {
  try {
    // تعطيل التنظيف التلقائي للغرف - الغرف تبقى دائماً
    // const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    // await VoiceRoom.updateMany(
    //   {
    //     lastActivity: { $lt: oneHourAgo },
    //     isActive: true,
    //     'participants.0': { $exists: false }
    //   },
    //   {
    //     isActive: false
    //   }
    // );

    // جلب جميع الغرف النشطة مع معلومات المالك
    const rooms = await VoiceRoom.find({
      isActive: true
    }).populate('ownerId', 'username profileImage').sort({ lastActivity: -1 });

    console.log('📋 جلب الغرف الشخصية من قاعدة البيانات:', rooms.length, 'غرفة');

    // طباعة تفاصيل المشاركين في كل غرفة
    rooms.forEach(room => {
      console.log(`🏠 غرفة "${room.name}" (مالك: ${room.ownerName}) - المشاركون: ${room.participants.length}`);
      room.participants.forEach((p, index) => {
        console.log(`  👤 ${index + 1}. ${p.username} (مقعد: ${p.seatIndex})`);
      });
    });

    // تحويل البيانات للتنسيق المطلوب للواجهة
    const roomsData = rooms.map(room => ({
      _id: room._id,
      id: room._id, // للتوافق مع الواجهة القديمة
      name: room.name, // الاسم المخصص من المالك
      owner: {
        id: room.ownerId?._id || room.ownerId,
        name: room.ownerName, // استخدام الاسم المحفوظ
        avatar: room.ownerId?.profileImage || '👤'
      },
      participants: room.participants || [],
      participantCount: room.participants?.length || 0,
      maxParticipants: room.maxParticipants, // 10 مشاركين
      isLocked: false, // الغرف الشخصية غير مقفلة افتراضياً
      createdAt: room.createdAt,
      lastActivity: room.lastActivity
    }));

    res.json(roomsData);

  } catch (error) {
    console.error('خطأ في جلب الغرف الصوتية:', error);
    res.status(500).json({ message: 'خطأ في جلب الغرف الصوتية' });
  }
});

// جلب تفاصيل غرفة صوتية واحدة
app.get('/api/voice-rooms/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;

    // البحث عن الغرفة بطريقتين: بـ MongoDB ObjectId أو بـ roomId field
    let room;
    if (mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId).populate('ownerId', 'username profileImage');
    } else {
      room = await VoiceRoom.findOne({ roomId }).populate('ownerId', 'username profileImage');
    }

    if (!room) {
      return res.status(404).json({ error: 'الغرفة غير موجودة' });
    }

    console.log(`🔍 جلب تفاصيل غرفة "${room.name}" - المشاركون: ${room.participants?.length || 0}`);

    // تحويل البيانات للتنسيق المطلوب للواجهة
    const roomData = {
      _id: room._id,
      id: room._id,
      name: room.name,
      owner: {
        id: room.ownerId?._id || room.ownerId,
        name: room.ownerName,
        avatar: room.ownerId?.profileImage || '👤'
      },
      participants: room.participants || [],
      participantCount: room.participants?.length || 0,
      maxParticipants: room.maxParticipants,
      isLocked: false,
      createdAt: room.createdAt,
      lastActivity: room.lastActivity
    };

    res.json(roomData);
  } catch (error) {
    console.error('خطأ في جلب تفاصيل الغرفة:', error);
    res.status(500).json({ error: 'خطأ في جلب تفاصيل الغرفة' });
  }
});

// تم حذف الـ endpoint المكرر - يتم استخدام الـ endpoint الموحد في السطر 3081

// تم حذف الـ endpoint المكرر - يتم استخدام الـ endpoint الموحد في السطر 3151

// حذف جميع الغرف الشخصية (للإدارة فقط) - يجب أن يأتي قبل المسار العام
app.delete('/api/voice-rooms/admin-delete-all', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ error: 'غير مصرح لك بحذف الغرف' });
    }

    const rooms = await VoiceRoom.find({ isActive: true });
    const deletedCount = rooms.length;

    // حذف جميع رسائل المحادثة
    await VoiceChat.deleteMany({});

    // حذف جميع الغرف الشخصية
    await VoiceRoom.deleteMany({ isActive: true });

    console.log(`🗑️ الإدارة حذفت جميع الغرف الشخصية (${deletedCount} غرفة)`);

    res.json({
      message: `تم حذف جميع الغرف الشخصية بنجاح (${deletedCount} غرفة)`,
      deletedCount: deletedCount,
      note: 'تذكير: سيتم إنشاء غرف جديدة تلقائياً عند دخول المستخدمين'
    });
  } catch (error) {
    console.error('خطأ في حذف جميع الغرف الشخصية:', error);
    res.status(500).json({ error: 'خطأ في حذف جميع الغرف الشخصية' });
  }
});

// حذف غرفة (للإدارة فقط)
app.delete('/api/voice-rooms/:roomId', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ error: 'غير مصرح لك بحذف الغرف' });
    }

    const { roomId } = req.params;
    await VoiceRoom.findByIdAndUpdate(roomId, { isActive: false });

    res.json({ message: 'تم حذف الغرفة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الغرفة:', error);
    res.status(500).json({ error: 'خطأ في حذف الغرفة' });
  }
});

// الانضمام للمايك (الضغط على مقعد)
app.post('/api/voice-rooms/:roomId/join-mic', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { seatIndex } = req.body;

    console.log(`🎤 محاولة انضمام للمايك - Room: ${roomId}, Seat: ${seatIndex}, User: ${req.user.userId}`);
    console.log('📋 Request params:', JSON.stringify(req.params));
    console.log('📋 Request body:', JSON.stringify(req.body));
    console.log('📋 Request URL:', req.url);
    console.log('📋 Original URL:', req.originalUrl);

    // البحث عن الغرفة بطرق متعددة: Player ID، MongoDB ObjectId، أو roomId field
    let room;

    // أولاً: محاولة البحث بـ Player ID (ownerPlayerId)
    room = await VoiceRoom.findOne({ ownerPlayerId: roomId, isActive: true });

    // ثانياً: محاولة البحث بـ MongoDB ObjectId
    if (!room && mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId);
    }

    // ثالثاً: البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId, isActive: true });
    }

    if (!room || !room.isActive) {
      console.log('❌ الغرفة غير موجودة أو غير نشطة');
      return res.status(404).json({ error: 'الغرفة غير موجودة' });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      console.log('❌ المستخدم غير موجود');
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    console.log(`👤 المستخدم: ${user.username}, المشاركون الحاليون: ${room.participants.length}`);

    // التحقق من أن المستخدم ليس على المايك بالفعل (له مقعد محدد)
    const existingParticipant = room.participants.find(p => p.userId.toString() === user._id.toString());
    if (existingParticipant && existingParticipant.seatIndex !== undefined && existingParticipant.seatIndex !== null) {
      console.log('❌ المستخدم على المايك بالفعل في المقعد:', existingParticipant.seatIndex);
      return res.status(400).json({ error: 'أنت على المايك بالفعل' });
    }

    // التحقق من أن المقعد فارغ (إذا تم تحديد مقعد معين)
    if (seatIndex !== undefined) {
      const seatTaken = room.participants.some(p => p.seatIndex === seatIndex);
      if (seatTaken) {
        console.log(`❌ المقعد ${seatIndex} محجوز`);
        return res.status(400).json({ error: 'المقعد محجوز' });
      }
      console.log(`✅ المقعد ${seatIndex} متاح`);
    }

    // إضافة المستخدم للمايك أو تحديث بياناته إذا كان موجوداً
    if (existingParticipant) {
      // تحديث المستخدم الموجود ليصبح على المايك
      existingParticipant.seatIndex = seatIndex;
      existingParticipant.isMuted = false;
      existingParticipant.isSpeaking = false;
      console.log(`🔄 تحديث المستخدم ${user.username} للمقعد ${seatIndex}`);
    } else {
      // إضافة مستخدم جديد للمايك
      room.participants.push({
        userId: user._id,
        username: user.username,
        avatar: user.profileImage || '👤',
        isMuted: false, // غير مكتوم عند الانضمام للمايك
        isSpeaking: false,
        seatIndex: seatIndex,
        joinedAt: new Date()
      });
      console.log(`➕ إضافة المستخدم ${user.username} للمقعد ${seatIndex}`);
    }

    room.lastActivity = new Date();
    await room.save();

    console.log(`🎤 ${user.username} انضم للمايك في غرفة "${room.name}"`);

    // بث تحديث انضمام المستخدم للمايك لجميع العملاء
    broadcastVoiceRoomUpdate(room._id.toString(), 'user_joined_mic', {
      user: {
        id: user._id,
        name: user.username,
        avatar: user.profilePicture || '/default-avatar.png',
        seatNumber: seatIndex
      },
      room: {
        _id: room._id,
        name: room.name,
        participants: room.participants,
        participantCount: room.participants.length
      }
    });

    // بث تحديث قائمة الغرف لجميع المستخدمين
    broadcastVoiceRoomUpdate('all', 'rooms_updated', {
      message: 'تم تحديث قائمة الغرف'
    });

    res.json({
      message: 'تم الانضمام للمايك بنجاح',
      participant: {
        userId: user._id,
        username: user.username,
        avatar: user.profileImage || '👤',
        isMuted: false,
        seatIndex: seatIndex
      }
    });
  } catch (error) {
    console.error('خطأ في الانضمام للمايك:', error);
    res.status(500).json({ error: 'خطأ في الانضمام للمايك' });
  }
});

// مغادرة المايك (العودة للمحادثة النصية)
app.post('/api/voice-rooms/:roomId/leave-mic', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;

    // البحث عن الغرفة بطريقتين: بـ MongoDB ObjectId أو بـ roomId field
    let room;

    // محاولة البحث بـ MongoDB ObjectId أولاً
    if (mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId);
    }

    // إذا لم توجد، البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId, isActive: true });
    }

    if (!room || !room.isActive) {
      return res.status(404).json({ error: 'الغرفة غير موجودة' });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // إزالة المستخدم من المايك
    room.participants = room.participants.filter(p => p.userId.toString() !== user._id.toString());
    room.lastActivity = new Date();
    await room.save();

    console.log(`💬 ${user.username} عاد للمحادثة النصية في غرفة "${room.name}"`);

    // بث تحديث مغادرة المستخدم للمايك لجميع العملاء
    broadcastVoiceRoomUpdate(room._id.toString(), 'user_left_mic', {
      user: {
        id: user._id,
        name: user.username,
        avatar: user.profileImage || '👤'
      },
      room: {
        _id: room._id,
        name: room.name,
        participants: room.participants,
        participantCount: room.participants.length
      }
    });

    // بث تحديث قائمة الغرف لجميع المستخدمين
    broadcastVoiceRoomUpdate('all', 'rooms_updated', {
      message: 'تم تحديث قائمة الغرف'
    });

    res.json({ message: 'تم العودة للمحادثة النصية' });
  } catch (error) {
    console.error('خطأ في مغادرة المايك:', error);
    res.status(500).json({ error: 'خطأ في مغادرة المايك' });
  }
});

// تنظيف المشاركين المكررين (للإدارة)
app.post('/api/voice-rooms/cleanup-duplicates', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ error: 'غير مصرح لك بهذا الإجراء' });
    }

    const rooms = await VoiceRoom.find({ isActive: true });
    let totalCleaned = 0;

    for (const room of rooms) {
      const uniqueParticipants = [];
      const seenUserIds = new Set();

      // إزالة المكررين
      for (const participant of room.participants) {
        const userIdStr = participant.userId.toString();
        if (!seenUserIds.has(userIdStr)) {
          seenUserIds.add(userIdStr);
          uniqueParticipants.push(participant);
        } else {
          totalCleaned++;
        }
      }

      // تحديث الغرفة إذا كان هناك مكررين
      if (uniqueParticipants.length !== room.participants.length) {
        room.participants = uniqueParticipants;
        await room.save();
        console.log(`🧹 تم تنظيف ${room.participants.length - uniqueParticipants.length} مشارك مكرر من غرفة "${room.name}"`);
      }
    }

    res.json({
      message: `تم تنظيف ${totalCleaned} مشارك مكرر من جميع الغرف`,
      cleanedCount: totalCleaned
    });
  } catch (error) {
    console.error('خطأ في تنظيف المكررين:', error);
    res.status(500).json({ error: 'خطأ في تنظيف المكررين' });
  }
});

// إعادة تعيين جميع الغرف (للإدارة)
app.post('/api/voice-rooms/reset-all', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ error: 'ليس لديك صلاحية للوصول' });
    }

    // إفراغ جميع المشاركين من جميع الغرف
    await VoiceRoom.updateMany(
      { isActive: true },
      { $set: { participants: [] } }
    );

    console.log('🧹 تم إعادة تعيين جميع الغرف الصوتية');

    res.json({
      message: 'تم إعادة تعيين جميع الغرف بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إعادة تعيين الغرف:', error);
    res.status(500).json({ error: 'خطأ في إعادة تعيين الغرف' });
  }
});





// حذف غرفة شخصية (للإدارة فقط)
app.delete('/api/voice-rooms/:roomId/admin-delete', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user || !user.isAdmin) {
      return res.status(403).json({ error: 'غير مصرح لك بحذف الغرف' });
    }

    const { roomId } = req.params;
    const room = await VoiceRoom.findById(roomId).populate('ownerId', 'username');

    if (!room) {
      return res.status(404).json({ error: 'الغرفة غير موجودة' });
    }

    // حذف الغرفة نهائياً
    await VoiceRoom.findByIdAndDelete(roomId);

    console.log(`🗑️ الإدارة حذفت الغرفة الشخصية "${room.name}" للمالك: ${room.ownerName} (ID: ${roomId})`);

    res.json({
      message: `تم حذف الغرفة الشخصية "${room.name}" للمالك ${room.ownerName} بنجاح`,
      deletedRoom: {
        id: room._id,
        name: room.name,
        owner: room.ownerName
      }
    });
  } catch (error) {
    console.error('خطأ في حذف الغرفة:', error);
    res.status(500).json({ error: 'خطأ في حذف الغرفة' });
  }
});

// الانضمام لغرفة صوتية
app.post('/api/voice-rooms/:roomId/join', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { username, avatar } = req.body;

    // البحث عن الغرفة بطرق متعددة: Player ID، MongoDB ObjectId، أو roomId field
    let room;

    // أولاً: محاولة البحث بـ Player ID (ownerPlayerId)
    room = await VoiceRoom.findOne({ ownerPlayerId: roomId, isActive: true }).populate('ownerId', 'username profileImage playerId');

    // ثانياً: محاولة البحث بـ MongoDB ObjectId
    if (!room && mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId).populate('ownerId', 'username profileImage playerId');
    }

    // ثالثاً: البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId, isActive: true }).populate('ownerId', 'username profileImage playerId');
    }

    if (!room || !room.isActive) {
      return res.status(404).json({ message: 'الغرفة غير موجودة' });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // التحقق من عدد المشاركين
    if (room.participants.length >= room.maxParticipants) {
      return res.status(400).json({ message: 'الغرفة ممتلئة' });
    }

    // التحقق من وجود المستخدم مسبقاً في هذه الغرفة
    const existingParticipant = room.participants.find(p => p.userId.toString() === req.user.userId);

    // التحقق من صاحب الغرفة - استخدام ownerId أو createdBy
    const isOwner = (room.ownerId && room.ownerId._id.toString() === req.user.userId) ||
                    (room.createdBy && room.createdBy.toString() === req.user.userId);

    // إذا كان المستخدم موجود بالفعل، إزالته أولاً ثم إعادة إضافته (لتحديث البيانات)
    if (existingParticipant) {
      room.participants = room.participants.filter(p => p.userId.toString() !== req.user.userId);
    }

    // التحقق من وجود المستخدم في غرفة أخرى
    const otherRoom = await VoiceRoom.findOne({
      isActive: true,
      _id: { $ne: room._id }, // استخدام _id بدلاً من roomId للمقارنة
      'participants.userId': req.user.userId
    });

    if (otherRoom) {
      // إزالة المستخدم من الغرفة الأخرى
      otherRoom.participants = otherRoom.participants.filter(p => p.userId.toString() !== req.user.userId);

      // الغرف تبقى نشطة دائماً - لا نلغي تفعيلها حتى لو لم يكن هناك مشاركون
      // if (otherRoom.participants.length === 0) {
      //   otherRoom.isActive = false;
      // }

      otherRoom.lastActivity = new Date();
      await otherRoom.save();
    }

    // إضافة المشارك (إذا لم يكن صاحب الغرفة)
    if (!isOwner) {
      room.participants.push({
        userId: req.user.userId,
        username: username || user.username || 'مستخدم',
        avatar: avatar || user.profilePicture || '👤',
        isMuted: true,
        isSpeaking: false,
        joinedAt: new Date()
      });
    }

    room.lastActivity = new Date();

    // حفظ الغرفة مع معالجة خطأ الإصدار
    try {
      await room.save();
    } catch (saveError) {
      if (saveError.name === 'VersionError') {
        // إعادة المحاولة مع بيانات محدثة
        const freshRoom = await VoiceRoom.findById(room._id);
        if (freshRoom && !isOwner && !freshRoom.participants.some(p => p.userId.toString() === req.user.userId)) {
          freshRoom.participants.push({
            userId: req.user.userId,
            username: username || user.username || 'مستخدم',
            avatar: avatar || user.profilePicture || '👤',
            isMuted: true,
            isSpeaking: false,
            joinedAt: new Date()
          });
          freshRoom.lastActivity = new Date();
          await freshRoom.save();
          room = freshRoom; // تحديث المرجع
        }
      } else {
        throw saveError;
      }
    }

    // إضافة رسالة ترحيب في المحادثة
    const systemUserId = new mongoose.Types.ObjectId('000000000000000000000000'); // ObjectId ثابت للنظام
    const welcomeMessage = new VoiceChat({
      roomId: room.roomId || room._id.toString(), // استخدام roomId إذا كان موجود، وإلا _id
      userId: systemUserId, // MongoDB ObjectId للتوافق
      userPlayerId: 'system', // Player ID للنظام
      username: 'System',
      avatar: '⚙️',
      message: `${username || user.username || 'مستخدم'} انضم إلى الغرفة! 👋`,
      type: 'system',
      timestamp: new Date()
    });

    await welcomeMessage.save();

    // بث رسالة الترحيب فوراً عبر WebSocket
    const welcomeMessageData = {
      _id: welcomeMessage._id,
      roomId: room.roomId || room._id.toString(),
      userId: systemUserId,
      username: 'System',
      avatar: '⚙️',
      message: `${username || user.username || 'مستخدم'} انضم إلى الغرفة! 👋`,
      type: 'system',
      timestamp: welcomeMessage.timestamp
    };

    // بث رسالة الترحيب لجميع المستخدمين في الغرفة
    const broadcastMessage = {
      type: 'voice_room_message',
      roomId: room.roomId || room._id.toString(),
      messageData: welcomeMessageData
    };

    console.log('📤 Broadcasting welcome message:', broadcastMessage);

    let sentCount = 0;
    wss.clients.forEach((client) => {
      if (client.readyState === 1) {
        client.send(JSON.stringify(broadcastMessage));
        sentCount++;
      }
    });

    console.log(`🎉 Welcome message sent to ${sentCount} clients`);

    res.json({
      message: isOwner ? 'تم دخول الغرفة بنجاح كصاحب الغرفة' : 'تم الانضمام للغرفة بنجاح',
      roomId: room.roomId || room._id.toString(),
      participantCount: room.participants.length,
      isOwner: isOwner
    });

  } catch (error) {
    console.error('خطأ في الانضمام للغرفة:', error);

    // معالجة خطأ الإصدار
    if (error.name === 'VersionError') {
      try {
        // إعادة المحاولة مع بيانات محدثة
        const freshRoom = await VoiceRoom.findById(room._id);
        if (freshRoom && !freshRoom.participants.some(p => p.userId.toString() === req.user.id)) {
          freshRoom.participants.push({
            userId: new mongoose.Types.ObjectId(req.user.id),
            joinedAt: new Date(),
            isOnMic: false,
            seatIndex: null
          });
          freshRoom.lastActivity = new Date();
          await freshRoom.save();

          return res.json({
            message: 'تم الانضمام للغرفة بنجاح',
            roomId: freshRoom.roomId || freshRoom._id.toString(),
            participantCount: freshRoom.participants.length
          });
        }
      } catch (retryError) {
        console.error('فشل في إعادة المحاولة:', retryError);
      }
    }

    res.status(500).json({ message: 'خطأ في الانضمام للغرفة' });
  }
});

// مغادرة غرفة صوتية
app.post('/api/voice-rooms/:roomId/leave', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;

    // البحث عن الغرفة بطرق متعددة: Player ID، MongoDB ObjectId، أو roomId field
    let room;

    // أولاً: محاولة البحث بـ Player ID (ownerPlayerId)
    room = await VoiceRoom.findOne({ ownerPlayerId: roomId, isActive: true });

    // ثانياً: محاولة البحث بـ MongoDB ObjectId
    if (!room && mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId);
    }

    // ثالثاً: البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId, isActive: true });
    }

    if (!room || !room.isActive) {
      return res.status(404).json({ message: 'الغرفة غير موجودة' });
    }

    // الحصول على اسم المستخدم قبل الإزالة
    const leavingUser = room.participants.find(p => p.userId.toString() === req.user.userId);
    const username = leavingUser ? leavingUser.username : 'مستخدم';

    // إزالة المشارك
    room.participants = room.participants.filter(p => p.userId.toString() !== req.user.userId);

    // إضافة رسالة مغادرة في المحادثة (إذا كان هناك مشاركون آخرون)
    if (room.participants.length > 0) {
      const systemUserId = new mongoose.Types.ObjectId('000000000000000000000000'); // ObjectId ثابت للنظام
      const leaveMessage = new VoiceChat({
        roomId: room.roomId || room._id.toString(),
        userId: systemUserId,
        username: 'System',
        avatar: '⚙️',
        message: `${username} غادر الغرفة 👋`,
        type: 'system',
        timestamp: new Date()
      });

      await leaveMessage.save();
    }

    // الغرف تبقى نشطة دائماً - لا نلغي تفعيلها حتى لو لم يكن هناك مشاركون
    // if (room.participants.length === 0) {
    //   room.isActive = false;
    // }

    room.lastActivity = new Date();
    await room.save();

    res.json({
      message: 'تم مغادرة الغرفة بنجاح',
      participantCount: room.participants.length
    });

  } catch (error) {
    console.error('خطأ في مغادرة الغرفة:', error);
    res.status(500).json({ message: 'خطأ في مغادرة الغرفة' });
  }
});

// تحديث حالة الميكروفون
app.post('/api/voice-rooms/:roomId/toggle-mic', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { isMuted, targetUserId } = req.body;

    const room = await VoiceRoom.findOne({ roomId: roomId, isActive: true });

    if (!room) {
      return res.status(404).json({ message: 'الغرفة غير موجودة' });
    }

    // تحديد المستخدم المستهدف (إما المستخدم نفسه أو مستخدم آخر)
    const userId = targetUserId || req.user.userId;

    // التحقق من الصلاحيات
    const isOwner = room.ownerId.toString() === req.user.userId;
    const isSelf = userId === req.user.userId;

    if (!isSelf && !isOwner) {
      return res.status(403).json({ message: 'ليس لديك صلاحية لكتم هذا المستخدم' });
    }

    // العثور على المشارك وتحديث حالة الميكروفون
    const participant = room.participants.find(p => p.userId.toString() === userId);
    if (participant) {
      participant.isMuted = isMuted;
      participant.isSpeaking = !isMuted;
      room.lastActivity = new Date();
      await room.save();
    }

    res.json({
      message: 'تم تحديث حالة الميكروفون',
      isMuted: isMuted,
      targetUserId: userId
    });

  } catch (error) {
    console.error('خطأ في تحديث حالة الميكروفون:', error);
    res.status(500).json({ message: 'خطأ في تحديث حالة الميكروفون' });
  }
});

// إرسال رسالة دردشة في الغرفة الصوتية
app.post('/api/voice-rooms/:roomId/chat', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { message, username, avatar } = req.body;

    if (!message || message.trim() === '') {
      return res.status(400).json({ message: 'الرسالة مطلوبة' });
    }

    // البحث عن الغرفة بطريقتين: بـ MongoDB ObjectId أو بـ roomId field
    let room;

    // محاولة البحث بـ MongoDB ObjectId أولاً
    if (mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId);
    }

    // إذا لم توجد، البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId, isActive: true });
    }
    if (!room) {
      return res.status(404).json({ message: 'الغرفة غير موجودة' });
    }

    // التحقق من أن المستخدم في الغرفة أو هو صاحب الغرفة
    const isParticipant = room.participants.some(p => p.userId.toString() === req.user.userId);
    const isOwner = room.createdBy.toString() === req.user.userId;

    if (!isParticipant && !isOwner) {
      return res.status(403).json({ message: 'يجب أن تكون في الغرفة لإرسال الرسائل' });
    }

    // الحصول على بيانات المستخدم
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // إنشاء الرسالة
    const chatMessage = new VoiceChat({
      roomId: roomId,
      userId: req.user.userId, // MongoDB ObjectId للتوافق
      userPlayerId: user.playerId, // Player ID الصغير
      username: username || user.username || 'مستخدم',
      avatar: avatar || '👤',
      message: message.trim(),
      type: 'user'
    });

    await chatMessage.save();

    res.json({
      message: 'تم إرسال الرسالة بنجاح',
      messageId: chatMessage._id
    });

  } catch (error) {
    console.error('خطأ في إرسال رسالة الدردشة:', error);
    res.status(500).json({ message: 'خطأ في إرسال الرسالة' });
  }
});

// جلب رسائل الدردشة للغرفة الصوتية
app.get('/api/voice-rooms/:roomId/chat', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const limit = parseInt(req.query.limit) || 50;

    // البحث عن الغرفة بطريقتين: بـ MongoDB ObjectId أو بـ roomId field
    let room;

    // محاولة البحث بـ MongoDB ObjectId أولاً
    if (mongoose.Types.ObjectId.isValid(roomId)) {
      room = await VoiceRoom.findById(roomId);
    }

    // إذا لم توجد، البحث بـ roomId field
    if (!room) {
      room = await VoiceRoom.findOne({ roomId: roomId });
    }

    if (!room) {
      return res.status(404).json({ message: 'الغرفة غير موجودة' });
    }

    // التحقق من أن المستخدم في الغرفة أو هو مالك الغرفة
    const isParticipant = room.participants.some(p => p.userId.toString() === req.user.userId);
    const isOwner = room.ownerId.toString() === req.user.userId;

    if (!isParticipant && !isOwner) {
      return res.status(403).json({ message: 'يجب أن تكون في الغرفة لرؤية الرسائل' });
    }

    // جلب الرسائل
    const messages = await VoiceChat.find({ roomId: roomId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .lean();

    // ترتيب الرسائل من الأقدم للأحدث
    messages.reverse();

    res.json(messages);

  } catch (error) {
    console.error('خطأ في جلب رسائل الدردشة:', error);
    res.status(500).json({ message: 'خطأ في جلب الرسائل' });
  }
});



// تنظيف المستخدم من جميع الغرف الصوتية (كمشارك فقط، ليس كصاحب غرفة)
app.post('/api/voice-rooms/cleanup-user', authenticateToken, async (req, res) => {
  try {
    // إزالة المستخدم من الغرف التي يشارك فيها فقط (ليس غرفته الشخصية)
    const rooms = await VoiceRoom.find({
      isActive: true,
      'participants.userId': req.user.userId,
      ownerId: { $ne: req.user.userId } // استثناء الغرفة الشخصية للمستخدم
    });

    for (const room of rooms) {
      const userParticipant = room.participants.find(p => p.userId.toString() === req.user.userId);

      if (userParticipant) {
        // إزالة المستخدم
        room.participants = room.participants.filter(p => p.userId.toString() !== req.user.userId);

        // إضافة رسالة مغادرة إذا كان هناك مشاركون آخرون
        if (room.participants.length > 0) {
          const systemUserId = new mongoose.Types.ObjectId('000000000000000000000000'); // ObjectId ثابت للنظام
          const leaveMessage = new VoiceChat({
            roomId: room.roomId,
            userId: systemUserId,
            username: 'System',
            avatar: '⚙️',
            message: `${userParticipant.username} غادر الغرفة 👋`,
            type: 'system',
            timestamp: new Date()
          });
          await leaveMessage.save();
        }

        room.lastActivity = new Date();
        await room.save();
      }
    }

    res.json({
      message: 'تم تنظيف المستخدم من الغرف كمشارك',
      roomsAffected: rooms.length
    });

  } catch (error) {
    console.error('خطأ في تنظيف المستخدم:', error);
    res.status(500).json({ message: 'خطأ في تنظيف المستخدم' });
  }
});

// إنشاء غرفة شخصية للمستخدم إذا لم تكن موجودة
app.post('/api/voice-rooms/create-personal', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // التحقق من وجود غرفة شخصية للمستخدم
    const existingRoom = await VoiceRoom.findOne({ ownerId: user._id, isActive: true });
    if (existingRoom) {
      return res.json({
        message: 'الغرفة الشخصية موجودة بالفعل',
        room: {
          _id: existingRoom._id,
          name: existingRoom.name,
          ownerName: existingRoom.ownerName
        }
      });
    }

    // إنشاء غرفة شخصية جديدة
    const personalRoom = new VoiceRoom({
      name: `غرفة ${user.username}`,
      ownerId: user._id,
      ownerName: user.username,
      participants: [],
      maxParticipants: 10,
      isActive: true
    });

    await personalRoom.save();
    console.log(`🎤 تم إنشاء غرفة شخصية للمستخدم: ${user.username} (ID: ${personalRoom._id})`);

    res.json({
      message: 'تم إنشاء الغرفة الشخصية بنجاح',
      room: {
        _id: personalRoom._id,
        name: personalRoom.name,
        ownerName: personalRoom.ownerName
      }
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الغرفة الشخصية:', error);
    res.status(500).json({ error: 'خطأ في إنشاء الغرفة الشخصية' });
  }
});

// تحديث اسم الغرفة الشخصية
app.put('/api/voice-rooms/update-name', authenticateToken, async (req, res) => {
  try {
    const { newName } = req.body;

    if (!newName || newName.trim().length === 0) {
      return res.status(400).json({ error: 'اسم الغرفة مطلوب' });
    }

    if (newName.trim().length > 50) {
      return res.status(400).json({ error: 'اسم الغرفة طويل جداً (الحد الأقصى 50 حرف)' });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // البحث عن الغرفة الشخصية للمستخدم
    const room = await VoiceRoom.findOne({ ownerId: user._id, isActive: true });
    if (!room) {
      return res.status(404).json({ error: 'الغرفة الشخصية غير موجودة' });
    }

    // تحديث اسم الغرفة
    room.name = newName.trim();
    room.lastActivity = new Date();
    await room.save();

    console.log(`✏️ ${user.username} غيّر اسم غرفته إلى: "${newName.trim()}"`);

    res.json({
      message: 'تم تحديث اسم الغرفة بنجاح',
      room: {
        _id: room._id,
        name: room.name,
        ownerName: room.ownerName
      }
    });

  } catch (error) {
    console.error('❌ خطأ في تحديث اسم الغرفة:', error);
    res.status(500).json({ error: 'خطأ في تحديث اسم الغرفة' });
  }
});

// تنظيف قاعدة البيانات من الغرف القديمة (للتطوير)
app.get('/cleanup-rooms-now', async (req, res) => {
  try {
    // حذف الـ collection بالكامل لإزالة جميع الـ indexes القديمة
    await VoiceRoom.collection.drop();
    console.log('🗑️ تم حذف collection الغرف الصوتية بالكامل');

    res.json({
      message: 'تم تنظيف قاعدة البيانات - حذف collection الغرف الصوتية بالكامل',
      deletedCount: 'all'
    });
  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
    res.status(500).json({ error: 'خطأ في تنظيف قاعدة البيانات' });
  }
});

// إنشاء غرف للمستخدمين الموجودين (للتطوير)
app.get('/create-missing-rooms-now', async (req, res) => {
  try {
    // حذف الـ indexes القديمة أولاً
    try {
      await VoiceRoom.collection.dropIndex('roomId_1');
      console.log('🗑️ تم حذف index roomId_1 القديم');
    } catch (indexError) {
      console.log('ℹ️ roomId index not found or already dropped');
    }

    // جلب جميع المستخدمين
    const allUsers = await User.find({});
    let createdRooms = 0;
    let errors = [];

    for (const user of allUsers) {
      try {
        // التحقق من وجود غرفة للمستخدم
        const existingRoom = await VoiceRoom.findOne({ ownerId: user._id });

        if (!existingRoom) {
          // إنشاء غرفة جديدة للمستخدم
          const personalRoom = new VoiceRoom({
            name: `غرفة ${user.username}`,
            ownerId: user._id,
            ownerName: user.username,
            participants: [],
            maxParticipants: 10,
            isActive: true
          });

          await personalRoom.save();
          createdRooms++;
          console.log(`🎤 تم إنشاء غرفة صوتية للمستخدم: ${user.username}`);
        }
      } catch (userError) {
        errors.push(`خطأ في إنشاء غرفة للمستخدم ${user.username}: ${userError.message}`);
        console.error(`❌ خطأ في إنشاء غرفة للمستخدم ${user.username}:`, userError);
      }
    }

    res.json({
      message: `تم إنشاء ${createdRooms} غرفة صوتية جديدة`,
      createdRooms,
      totalUsers: allUsers.length,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الغرف المفقودة:', error);
    res.status(500).json({ error: 'خطأ في إنشاء الغرف المفقودة' });
  }
});

// إعادة توجيه أي طلب غير API إلى index.html لتطبيق React
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'public', 'index.html'));
});

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'حدث خطأ في الخادم' });
});

// معالج الطرق غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({ message: 'الطريق غير موجود' });
});

// أنشئ خادم HTTP يدويًا لإرفاق WebSocket
const httpServer = http.createServer(app);

// إعداد Socket.IO للغرف الصوتية
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: ['https://infinity-box25.netlify.app', 'http://localhost:3000', 'http://localhost:5173'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// إعداد WebSocket على المسار /ws
const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

// تخزين معلومات العملاء المتصلين
const connectedClients = new Map(); // userId -> { socket, currentRoomId, userInfo }

// دالة لبث تحديثات الغرف الصوتية للمستخدمين في نفس الغرفة فقط
function broadcastVoiceRoomUpdate(roomId, updateType, data) {
  const broadcastMessage = {
    type: 'voice_room_update',
    roomId: roomId,
    updateType: updateType,
    data: data
  };

  console.log('📤 Broadcasting message to room:', roomId, 'Type:', updateType);

  let sentCount = 0;
  connectedClients.forEach((clientInfo, userId) => {
    // إرسال فقط للمستخدمين في نفس الغرفة أو تحديث قائمة الغرف العامة
    if (clientInfo.socket.readyState === 1 &&
        (clientInfo.currentRoomId === roomId || updateType === 'room_list_update')) {
      clientInfo.socket.send(JSON.stringify(broadcastMessage));
      sentCount++;
      console.log(`📤 Sent to user ${userId} in room ${clientInfo.currentRoomId}`);
    }
  });

  console.log(`🔄 Voice room update (${updateType}) sent to ${sentCount} clients for room ${roomId}`);
}

// دالة لبث رسائل الدردشة للمستخدمين في نفس الغرفة فقط
function broadcastVoiceRoomMessage(roomId, messageData) {
  const broadcastMessage = {
    type: 'voice_room_message',
    roomId: roomId,
    messageData: messageData
  };

  console.log('💬 Broadcasting chat message to room:', roomId);

  let sentCount = 0;
  connectedClients.forEach((clientInfo, userId) => {
    // إرسال فقط للمستخدمين في نفس الغرفة
    if (clientInfo.socket.readyState === 1 && clientInfo.currentRoomId === roomId) {
      clientInfo.socket.send(JSON.stringify(broadcastMessage));
      sentCount++;
      console.log(`💬 Chat sent to user ${userId} in room ${roomId}`);
    }
  });

  console.log(`💬 Chat message sent to ${sentCount} users in room ${roomId}`);
}

wss.on('connection', (socket) => {
  console.log('🔌 WebSocket client connected');
  socket.send(JSON.stringify({ type: 'connection_established' }));

  // متغير لتخزين معلومات المستخدم لهذا الاتصال
  let currentUserId = null;
  let currentRoomId = null;

  socket.on('message', (data) => {
    try {
      const textData = typeof data === 'string' ? data : data.toString();
      const message = JSON.parse(textData);

      console.log('📨 WebSocket message received:', message.type);

      // تسجيل المستخدم في الغرفة
      if (message.type === 'join_voice_room') {
        currentUserId = message.data.userId;
        currentRoomId = message.data.roomId;

        // تحديث معلومات العميل
        connectedClients.set(currentUserId, {
          socket: socket,
          currentRoomId: currentRoomId,
          userInfo: message.data.userInfo || {}
        });

        console.log(`👤 User ${currentUserId} joined room ${currentRoomId}`);
      }

      // مغادرة الغرفة
      else if (message.type === 'leave_voice_room') {
        if (currentUserId) {
          const clientInfo = connectedClients.get(currentUserId);
          if (clientInfo) {
            clientInfo.currentRoomId = null;
            connectedClients.set(currentUserId, clientInfo);
          }
        }
        currentRoomId = null;
        console.log(`👤 User ${currentUserId} left room`);
      }

      // رسائل المحادثة الخاصة
      else if (message.type === 'private_message') {
        // رسالة خاصة - بث للجميع (سيتم تصفيتها في العميل)
        const broadcastMessage = {
          type: 'new_message',
          messageData: message.data.messageData,
          recipientId: message.data.recipientId,
          senderId: message.data.messageData.sender._id
        };

        console.log('📤 Broadcasting message to all clients:', {
          recipientId: broadcastMessage.recipientId,
          senderId: broadcastMessage.senderId,
          content: message.data.messageData.content
        });

        let sentCount = 0;
        wss.clients.forEach((client) => {
          if (client.readyState === 1) {
            client.send(JSON.stringify(broadcastMessage));
            sentCount++;
          }
        });

        console.log(`📡 Message sent to ${sentCount} connected clients`);
      }

      // رسائل الغرف الصوتية - استخدام الدالة الجديدة للبث المصفى
      else if (message.type === 'voice_room_message') {
        console.log('🎤 Voice room message received:', message.data);

        // استخدام الدالة الجديدة للبث المصفى حسب الغرفة
        broadcastVoiceRoomMessage(message.data.roomId, message.data.messageData);
      }

      // تحديثات الغرف الصوتية (انضمام، مغادرة، إلخ)
      else if (message.type === 'voice_room_update') {
        console.log('🔄 Voice room update:', message.data);

        const broadcastMessage = {
          type: 'voice_room_update',
          roomId: message.data.roomId,
          updateType: message.data.updateType,
          data: message.data.data
        };

        let sentCount = 0;
        wss.clients.forEach((client) => {
          if (client.readyState === 1) {
            client.send(JSON.stringify(broadcastMessage));
            sentCount++;
          }
        });

        console.log(`🔄 Voice room update sent to ${sentCount} clients`);
      } else {
        // رسائل أخرى - بث عادي
        wss.clients.forEach((client) => {
          if (client.readyState === 1) {
            client.send(textData);
          }
        });
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      // في حالة الخطأ، بث الرسالة كما هي
      const textData = typeof data === 'string' ? data : data.toString();
      wss.clients.forEach((client) => {
        if (client.readyState === 1) {
          client.send(textData);
        }
      });
    }
  });

  socket.on('close', async () => {
    console.log('🛑 WebSocket client disconnected');

    // إزالة المستخدم من قائمة العملاء المتصلين
    if (currentUserId) {
      connectedClients.delete(currentUserId);
      console.log(`🗑️ Removed user ${currentUserId} from connected clients`);
    }

    // تنظيف المستخدمين غير النشطين من الغرف الصوتية
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

      // البحث عن الغرف التي تحتوي على مشاركين غير نشطين
      const rooms = await VoiceRoom.find({ isActive: true });

      for (const room of rooms) {
        let participantsChanged = false;

        // إزالة المشاركين الذين لم يكونوا نشطين لأكثر من 5 دقائق
        const activeParticipants = room.participants.filter(p => {
          const isActive = p.joinedAt > fiveMinutesAgo;
          if (!isActive) participantsChanged = true;
          return isActive;
        });

        if (participantsChanged) {
          room.participants = activeParticipants;

          // الغرف تبقى نشطة دائماً - لا نلغي تفعيلها حتى لو لم يكن هناك مشاركون
          // if (room.participants.length === 0) {
          //   room.isActive = false;
          // }

          // التأكد من وجود ownerPlayerId قبل الحفظ
          if (!room.ownerPlayerId && room.ownerId) {
            try {
              const owner = await User.findById(room.ownerId);
              if (owner && owner.playerId) {
                room.ownerPlayerId = owner.playerId;
              }
            } catch (err) {
              console.error('خطأ في جلب بيانات المالك:', err);
            }
          }

          room.lastActivity = new Date();
          await room.save();

          console.log(`🧹 تم تنظيف المشاركين غير النشطين من غرفة "${room.name}" - المشاركون الحاليون: ${room.participants.length}`);
        }
      }
    } catch (error) {
      console.error('خطأ في تنظيف المستخدمين غير النشطين:', error);
    }
  });
});

// دالة حذف المحادثات القديمة (أكثر من 3 أيام)
const deleteOldMessages = async () => {
  try {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    const result = await Message.deleteMany({
      createdAt: { $lt: threeDaysAgo }
    });

    if (result.deletedCount > 0) {
      console.log(`🗑️ Deleted ${result.deletedCount} old messages (older than 3 days)`);
    }
  } catch (error) {
    console.error('Error deleting old messages:', error);
  }
};

// API endpoints للغرف الصوتية - تم نقلها إلى الأعلى لتجنب التكرار

// تشغيل مهمة حذف المحادثات القديمة كل يوم في الساعة 2:00 صباحاً
cron.schedule('0 2 * * *', () => {
  console.log('🕐 Running daily cleanup of old messages...');
  deleteOldMessages();
});

// تشغيل حذف المحادثات القديمة عند بدء تشغيل السيرفر
console.log('🧹 Running initial cleanup of old messages...');
deleteOldMessages();

// إعداد Socket.IO للغرف الصوتية
const voiceRooms = new Map(); // roomId -> Set of socket IDs
const userSockets = new Map(); // userId -> socket

// التحقق من صحة التوكن لـ Socket.IO
const authenticateSocketToken = (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return next(new Error('No token provided'));
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.userId;
    next();
  } catch (error) {
    next(new Error('Invalid token'));
  }
};

io.use(authenticateSocketToken);

io.on('connection', (socket) => {
  console.log(`🔌 Socket.IO user connected: ${socket.userId}`);
  userSockets.set(socket.userId, socket);

  // الانضمام لغرفة صوتية
  socket.on('join-voice-room', async (data) => {
    try {
      const { roomId, userId } = data;

      // التحقق من وجود الغرفة
      const room = await VoiceRoom.findById(roomId);
      if (!room || !room.isActive) {
        socket.emit('error', { message: 'الغرفة غير موجودة أو غير نشطة' });
        return;
      }

      // الانضمام لغرفة Socket.IO
      socket.join(roomId);

      // إضافة المستخدم لخريطة الغرف
      if (!voiceRooms.has(roomId)) {
        voiceRooms.set(roomId, new Set());
      }
      voiceRooms.get(roomId).add(socket.id);

      // الحصول على معلومات المستخدم
      const user = await User.findById(userId);
      if (!user) {
        socket.emit('error', { message: 'المستخدم غير موجود' });
        return;
      }

      // إشعار المستخدمين الآخرين في الغرفة
      socket.to(roomId).emit('user-joined', {
        user: {
          id: userId,
          name: user.username,
          avatar: user.profileImage || '👤',
          isMuted: false,
          isSpeaking: false,
          seatIndex: null
        }
      });

      console.log(`🎤 User ${user.username} joined voice room ${roomId}`);
    } catch (error) {
      console.error('Error joining voice room:', error);
      socket.emit('error', { message: 'خطأ في الانضمام للغرفة الصوتية' });
    }
  });

  // مغادرة الغرفة الصوتية
  socket.on('leave-voice-room', (data) => {
    const { roomId, userId } = data;

    socket.leave(roomId);

    // إزالة من خريطة الغرف
    if (voiceRooms.has(roomId)) {
      voiceRooms.get(roomId).delete(socket.id);
      if (voiceRooms.get(roomId).size === 0) {
        voiceRooms.delete(roomId);
      }
    }

    // إشعار المستخدمين الآخرين
    socket.to(roomId).emit('user-left', { userId });

    console.log(`🎤 User ${userId} left voice room ${roomId}`);
  });

  // إشارات WebRTC
  socket.on('webrtc-signal', (data) => {
    const { to, signal, roomId } = data;
    const targetSocket = userSockets.get(to);

    if (targetSocket) {
      targetSocket.emit('webrtc-signal', {
        from: socket.userId,
        signal: signal
      });
    }
  });

  // حالة كتم المستخدم
  socket.on('user-muted', (data) => {
    const { roomId, isMuted } = data;
    socket.to(roomId).emit('user-muted', {
      userId: socket.userId,
      isMuted
    });
  });

  // حالة تحدث المستخدم
  socket.on('user-speaking', (data) => {
    const { roomId, isSpeaking } = data;
    socket.to(roomId).emit('user-speaking', {
      userId: socket.userId,
      isSpeaking
    });
  });

  // قطع الاتصال
  socket.on('disconnect', () => {
    console.log(`🔌 Socket.IO user disconnected: ${socket.userId}`);

    // إزالة من جميع الغرف
    voiceRooms.forEach((sockets, roomId) => {
      if (sockets.has(socket.id)) {
        sockets.delete(socket.id);
        socket.to(roomId).emit('user-left', { userId: socket.userId });

        if (sockets.size === 0) {
          voiceRooms.delete(roomId);
        }
      }
    });

    userSockets.delete(socket.userId);
  });
});

httpServer.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log('🎤 Socket.IO voice chat enabled');
  console.log('📅 Message cleanup scheduled: Daily at 2:00 AM (messages older than 3 days will be deleted)');
});