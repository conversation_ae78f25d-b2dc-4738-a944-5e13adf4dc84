import React, { useState, useEffect } from 'react';
import {
  Gamepad2,
  Users,
  Trophy,
  Settings,
  LogOut,
  Coins,
  Star,
  Crown,
  Box,
  Infinity,
  Menu,
  X,
  Shield,
  Mic
} from 'lucide-react';
import GameGrid from './GameGrid';
import AdminDashboard from './AdminDashboard';
import MobileProfileCard from './MobileProfileCard';
import SimpleVoiceRooms from './SimpleVoiceRooms';

interface MainDashboardProps {
  userData: any;
  onLogout: () => void;
  onUpdateProfile?: (data: any) => void;
}

const MainDashboard: React.FC<MainDashboardProps> = ({ userData, onLogout, onUpdateProfile }) => {
  const [activeTab, setActiveTab] = useState<'games' | 'profile' | 'admin' | 'voice-rooms'>('games');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [currentUserData, setCurrentUserData] = useState(userData);

  // دالة تحديث الملف الشخصي
  const handleProfileUpdate = (updatedData: any) => {
    console.log('🔄 MainDashboard: Updating profile data:', updatedData);
    const newUserData = { ...currentUserData, ...updatedData };
    setCurrentUserData(newUserData);

    // تحديث البيانات في الشريط الجانبي أيضاً
    if (onUpdateProfile) {
      onUpdateProfile(newUserData);
    }
  };



  const navigationItems = [
    { id: 'games', label: 'الألعاب', icon: Gamepad2, color: 'text-blue-400' },
    { id: 'profile', label: 'الملف الشخصي', icon: Users, color: 'text-blue-300' },
    ...(userData?.isAdmin ? [{ id: 'admin', label: 'لوحة المشرف', icon: Shield, color: 'text-amber-300' }] : []),
  ];

  // تحديث بيانات المستخدم
  useEffect(() => {
    setCurrentUserData(userData);
  }, [userData]);

  const renderContent = () => {
    switch (activeTab) {
      case 'games':
        return <GameGrid setActiveTab={setActiveTab} />;
      case 'voice-rooms':
        return <SimpleVoiceRooms userData={currentUserData} onBack={() => setActiveTab('games')} />;
      case 'profile':
        return <ProfileContent userData={currentUserData} onUpdateProfile={handleProfileUpdate} />;
      case 'admin':
        return userData?.isAdmin ? <AdminDashboard userData={userData} onLogout={onLogout} /> : <GameGrid />;
      default:
        return <GameGrid />;
    }
  };

  // إذا كان المستخدم في لوحة المشرف أو الغرف الصوتية، عرضها بدون الشريط الجانبي
  if (activeTab === 'admin' && userData?.isAdmin) {
    return <AdminDashboard userData={userData} onLogout={onLogout} />;
  }

  if (activeTab === 'voice-rooms') {
    return <SimpleVoiceRooms userData={currentUserData} onBack={() => setActiveTab('games')} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white relative overflow-hidden particle-effect">
      {/* خلفية متحركة مطابقة تماماً لصفحة تسجيل الدخول */}
      <div className="absolute inset-0 overflow-hidden">
        {/* نجوم متحركة في السماء الليلية */}
        <div className="absolute inset-0 opacity-20">
          <div className="grid grid-cols-16 gap-6 h-full w-full p-4">
            {Array.from({ length: 80 }).map((_, i) => (
              <div
                key={i}
                className={`${i % 4 === 0 ? 'w-1 h-1' : 'w-0.5 h-0.5'} bg-white rounded-full animate-pulse`}
                style={{
                  animationDelay: `${i * 150}ms`,
                  animationDuration: `${2 + (i % 3)}s`
                }}
              ></div>
            ))}
          </div>
        </div>

        {/* قمر في الخلفية */}
        <div className="absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"></div>

        {/* تأثير خلفي متحرك - رمز اللانهاية */}
        <div className="absolute inset-0 flex items-center justify-center opacity-5">
          <div className="text-8xl font-black text-white animate-spin" style={{ animationDuration: '25s' }}>∞</div>
        </div>
      </div>

      <div className="relative z-10 flex h-screen">
        {/* الشريط الجانبي */}
        <aside className={`fixed lg:static inset-y-0 right-0 z-50 w-80 crystal-sidebar transform transition-transform duration-300 ${
          isSidebarOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'
        }`}>
          <div className="flex flex-col h-full">
            {/* رأس الشريط الجانبي */}
            <div className="p-6 border-b border-blue-400/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* الشعار الدوار الجديد */}
                  <div className="relative group logo-crystal-effect">
                    {/* الكرة ثلاثية الأبعاد - دوران طولي مثل الكرة الأرضية */}
                    <div className="w-12 h-12 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y crystal-hover-lift">
                      {/* طبقة الألوان المتحركة */}
                      <div className="color-layer"></div>

                      {/* النص داخل الكرة ثلاثية الأبعاد */}
                      <div className="text-center relative z-10">
                        <div className="bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-xs leading-tight drop-shadow-2xl" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8), 0 0 5px rgba(255,255,255,0.3)' }}>
                          INFINITY
                        </div>
                        <div className="bg-gradient-to-r from-blue-200 via-indigo-100 to-yellow-200 bg-clip-text text-transparent font-black text-xs leading-tight drop-shadow-2xl" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8), 0 0 5px rgba(255,255,255,0.3)' }}>
                          BOX
                        </div>
                      </div>

                      {/* أيقونة اللانهاية */}
                      <Infinity className="w-3 h-3 text-white absolute -top-1 -right-1 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-0.5 z-20" style={{ animationDuration: '12s' }} />
                    </div>

                    {/* هالة متوهجة كروية - دوران طولي معاكس */}
                    <div className="absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"></div>

                    {/* دوائر إضافية للتأثير - حلقات مدارية طولية */}
                    <div className="absolute inset-0 rounded-full border border-white/10 rotate-y-fast"></div>
                    <div className="absolute -inset-1 rounded-full border border-white/5 rotate-y-slow"></div>
                  </div>

                  <div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-yellow-300 via-amber-200 to-blue-300 bg-clip-text text-transparent">
                      INFINITY BOX
                    </h1>
                    <p className="text-xs text-blue-300">منصة الألعاب والمحادثة</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsSidebarOpen(false)}
                  className="lg:hidden p-2 rounded-lg hover:bg-slate-700 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* معلومات المستخدم */}
            <div className="p-6 border-b border-slate-700">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center text-xl font-bold">
                  {userData?.profileImage ? (
                    <img
                      src={userData.profileImage}
                      alt="الصورة الشخصية"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-white">
                      {userData?.username?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-white">{userData?.username || 'مستخدم'}</h3>
                  {userData?.playerId && (
                    <div className="text-xs text-gray-400 mt-1">رقمك المميز: {userData.playerId}</div>
                  )}
                  <div className="flex items-center gap-2 text-sm text-gray-400">
                    <Star className="w-4 h-4 text-yellow-400" />
                    <span>المستوى {userData?.level || 1}</span>
                  </div>
                </div>
                {userData?.isAdmin && (
                  <Crown className="w-5 h-5 text-yellow-400" />
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="crystal-card rounded-xl p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Coins className="w-4 h-4 text-yellow-400" />
                    <span className="text-yellow-400 font-semibold">1,250</span>
                  </div>
                  <p className="text-xs text-gray-400">العملات الذهبية</p>
                </div>
                <div className="crystal-card rounded-xl p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Trophy className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-semibold">847</span>
                  </div>
                  <p className="text-xs text-gray-400">النقاط</p>
                </div>
              </div>
            </div>

            {/* التنقل */}
            <nav className="flex-1 p-6">
              <div className="space-y-2">
                {navigationItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveTab(item.id as any);
                      setIsSidebarOpen(false);
                    }}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 text-right crystal-nav-item ${
                      activeTab === item.id
                        ? 'active'
                        : ''
                    }`}
                  >
                    <item.icon className={`w-5 h-5 ${item.color}`} />
                    <span className="font-medium">{item.label}</span>
                    {item.id === 'admin' && userData?.isAdmin && (
                      <Crown className="w-4 h-4 text-yellow-400 mr-auto" />
                    )}
                  </button>
                ))}
              </div>
            </nav>

            {/* أزرار الإعدادات والغرفة الصوتية */}
            <div className="p-6 border-t border-white/10 space-y-2">
              <button
                className="w-full flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-white/5 transition-colors text-right"
                onClick={() => {
                  if (userData?.isAdmin) {
                    setActiveTab('admin');
                    setIsSidebarOpen(false);
                  } else {
                    // يمكنك هنا فتح صفحة إعدادات عادية أو نافذة إعدادات
                    alert('هذه الصفحة مخصصة للمشرفين فقط');
                  }
                }}
              >
                <Settings className="w-5 h-5 text-gray-400" />
                <span>الإعدادات</span>
              </button>
              <button
                onClick={() => {
                  setActiveTab('voice-rooms');
                  setIsSidebarOpen(false);
                }}
                className="w-full flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-blue-500/20 text-blue-300 transition-colors text-right"
              >
                <Mic className="w-5 h-5" />
                <span>غرفتي الصوتية</span>
              </button>
            </div>
          </div>
        </aside>

        {/* المحتوى الرئيسي */}
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* شريط علوي للجوال */}
          <header className="lg:hidden crystal-header p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* الشعار الدوار للجوال */}
                <div className="relative group">
                  <div className="w-8 h-8 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y">
                    <div className="color-layer"></div>
                    <div className="text-center relative z-10">
                      <div className="bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-xs leading-tight drop-shadow-2xl" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
                        ∞
                      </div>
                    </div>
                    <Infinity className="w-2 h-2 text-white absolute -top-0.5 -right-0.5 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-0.5 z-20" style={{ animationDuration: '12s' }} />
                  </div>
                  <div className="absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"></div>
                  <div className="absolute inset-0 rounded-full border border-white/10 rotate-y-fast"></div>
                </div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent">INFINITY BOX</h1>
              </div>
              <button
                onClick={() => setIsSidebarOpen(true)}
                className="p-2 rounded-lg hover:bg-slate-700 transition-colors"
              >
                <Menu className="w-6 h-6" />
              </button>
            </div>
          </header>

          {/* المحتوى */}
          <div className="flex-1 overflow-auto p-6 lg:p-8">
            {renderContent()}
          </div>
        </main>
      </div>

      {/* خلفية الشريط الجانبي للجوال */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
    </div>
  );
};

// مكونات المحتوى
const ProfileContent: React.FC<{ userData: any; onUpdateProfile: (data: any) => void }> = ({ userData, onUpdateProfile }) => (
  <MobileProfileCard userData={userData} isOwner={true} onUpdateProfile={onUpdateProfile} />
);






export default MainDashboard;