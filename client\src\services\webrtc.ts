import Peer from 'simple-peer';
import { io, Socket } from 'socket.io-client';

export interface VoiceUser {
  id: string;
  name: string;
  avatar: string;
  isMuted: boolean;
  isSpeaking: boolean;
  seatIndex: number;
}

export interface WebRTCEvents {
  userJoined: (user: VoiceUser) => void;
  userLeft: (userId: string) => void;
  userMuted: (userId: string, isMuted: boolean) => void;
  userSpeaking: (userId: string, isSpeaking: boolean) => void;
  error: (error: string) => void;
  connected: () => void;
  disconnected: () => void;
}

class WebRTCService {
  private socket: Socket | null = null;
  private localStream: MediaStream | null = null;
  private peers: Map<string, Peer.Instance> = new Map();
  private audioElements: Map<string, HTMLAudioElement> = new Map();
  private currentRoomId: string | null = null;
  private currentUserId: string | null = null;
  private listeners: Map<keyof WebRTCEvents, Set<Function>> = new Map();
  private isInitialized = false;
  private isMuted = false;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private speakingThreshold = 30;
  private speakingCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.error('Failed to initialize AudioContext:', error);
    }
  }

  async initialize(userId: string, token: string): Promise<void> {
    if (this.isInitialized) return;

    try {
      // إنشاء اتصال Socket.IO
      this.socket = io('/', {
        auth: { token },
        transports: ['websocket', 'polling']
      });

      this.currentUserId = userId;

      // إعداد مستمعي الأحداث
      this.setupSocketListeners();

      // طلب إذن الوصول للمايكروفون
      await this.requestMicrophoneAccess();

      this.isInitialized = true;
      this.emit('connected');

      console.log('🎤 WebRTC Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error);
      this.emit('error', 'فشل في تهيئة خدمة الصوت');
      throw error;
    }
  }

  private async requestMicrophoneAccess(): Promise<void> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });

      // إعداد كشف الصوت
      this.setupVoiceDetection();

      console.log('🎤 Microphone access granted');
    } catch (error) {
      console.error('Microphone access denied:', error);
      throw new Error('يجب السماح بالوصول للمايكروفون لاستخدام الغرف الصوتية');
    }
  }

  private setupVoiceDetection() {
    if (!this.localStream || !this.audioContext) return;

    try {
      const source = this.audioContext.createMediaStreamSource(this.localStream);
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      source.connect(this.analyser);

      // بدء مراقبة مستوى الصوت
      this.startSpeakingDetection();
    } catch (error) {
      console.error('Failed to setup voice detection:', error);
    }
  }

  private startSpeakingDetection() {
    if (!this.analyser) return;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    this.speakingCheckInterval = setInterval(() => {
      if (!this.analyser || this.isMuted) return;

      this.analyser.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;

      const isSpeaking = average > this.speakingThreshold;
      
      // إرسال حالة التحدث للخادم
      if (this.socket && this.currentRoomId) {
        this.socket.emit('user-speaking', {
          roomId: this.currentRoomId,
          isSpeaking
        });
      }

      this.emit('userSpeaking', this.currentUserId!, isSpeaking);
    }, 100);
  }

  private setupSocketListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('🔌 Socket.IO connected');
    });

    this.socket.on('disconnect', () => {
      console.log('🔌 Socket.IO disconnected');
      this.emit('disconnected');
    });

    // مستخدم جديد انضم للغرفة
    this.socket.on('user-joined', (data: { user: VoiceUser; signal: any }) => {
      console.log('👤 User joined:', data.user.name);
      this.createPeerConnection(data.user.id, false, data.signal);
      this.emit('userJoined', data.user);
    });

    // مستخدم غادر الغرفة
    this.socket.on('user-left', (data: { userId: string }) => {
      console.log('👤 User left:', data.userId);
      this.removePeerConnection(data.userId);
      this.emit('userLeft', data.userId);
    });

    // إشارة WebRTC
    this.socket.on('webrtc-signal', (data: { from: string; signal: any }) => {
      const peer = this.peers.get(data.from);
      if (peer) {
        peer.signal(data.signal);
      }
    });

    // حالة كتم المستخدم
    this.socket.on('user-muted', (data: { userId: string; isMuted: boolean }) => {
      this.emit('userMuted', data.userId, data.isMuted);
    });

    // حالة تحدث المستخدم
    this.socket.on('user-speaking', (data: { userId: string; isSpeaking: boolean }) => {
      this.emit('userSpeaking', data.userId, data.isSpeaking);
    });
  }

  async joinRoom(roomId: string): Promise<void> {
    if (!this.socket || !this.isInitialized) {
      throw new Error('WebRTC service not initialized');
    }

    this.currentRoomId = roomId;

    // الانضمام للغرفة عبر Socket.IO
    this.socket.emit('join-voice-room', {
      roomId,
      userId: this.currentUserId
    });

    console.log(`🏠 Joined voice room: ${roomId}`);
  }

  async leaveRoom(): Promise<void> {
    if (!this.socket || !this.currentRoomId) return;

    // مغادرة الغرفة
    this.socket.emit('leave-voice-room', {
      roomId: this.currentRoomId,
      userId: this.currentUserId
    });

    // إغلاق جميع الاتصالات
    this.peers.forEach((peer, userId) => {
      this.removePeerConnection(userId);
    });

    this.currentRoomId = null;
    console.log('🏠 Left voice room');
  }

  private createPeerConnection(userId: string, initiator: boolean, signal?: any): void {
    if (this.peers.has(userId)) return;

    const peer = new Peer({
      initiator,
      trickle: false,
      stream: this.localStream || undefined
    });

    // إرسال الإشارة
    peer.on('signal', (data) => {
      if (this.socket && this.currentRoomId) {
        this.socket.emit('webrtc-signal', {
          to: userId,
          signal: data,
          roomId: this.currentRoomId
        });
      }
    });

    // استقبال الصوت
    peer.on('stream', (stream) => {
      this.playRemoteStream(userId, stream);
    });

    // خطأ في الاتصال
    peer.on('error', (error) => {
      console.error(`Peer connection error with ${userId}:`, error);
      this.removePeerConnection(userId);
    });

    // إغلاق الاتصال
    peer.on('close', () => {
      console.log(`Peer connection closed with ${userId}`);
      this.removePeerConnection(userId);
    });

    this.peers.set(userId, peer);

    // إذا كانت هناك إشارة، قم بمعالجتها
    if (signal) {
      peer.signal(signal);
    }
  }

  private playRemoteStream(userId: string, stream: MediaStream): void {
    // إنشاء عنصر صوتي جديد
    const audio = new Audio();
    audio.srcObject = stream;
    audio.autoplay = true;
    audio.volume = 1.0;

    // إضافة العنصر للخريطة
    this.audioElements.set(userId, audio);

    console.log(`🔊 Playing audio stream from user: ${userId}`);
  }

  private removePeerConnection(userId: string): void {
    // إغلاق اتصال Peer
    const peer = this.peers.get(userId);
    if (peer) {
      peer.destroy();
      this.peers.delete(userId);
    }

    // إيقاف عنصر الصوت
    const audio = this.audioElements.get(userId);
    if (audio) {
      audio.pause();
      audio.srcObject = null;
      this.audioElements.delete(userId);
    }
  }

  toggleMute(): boolean {
    if (!this.localStream) return false;

    this.isMuted = !this.isMuted;
    
    // كتم/إلغاء كتم المايكروفون
    this.localStream.getAudioTracks().forEach(track => {
      track.enabled = !this.isMuted;
    });

    // إرسال حالة الكتم للخادم
    if (this.socket && this.currentRoomId) {
      this.socket.emit('user-muted', {
        roomId: this.currentRoomId,
        isMuted: this.isMuted
      });
    }

    console.log(`🎤 Microphone ${this.isMuted ? 'muted' : 'unmuted'}`);
    return this.isMuted;
  }

  setVolume(userId: string, volume: number): void {
    const audio = this.audioElements.get(userId);
    if (audio) {
      audio.volume = Math.max(0, Math.min(1, volume));
    }
  }

  on<K extends keyof WebRTCEvents>(event: K, callback: WebRTCEvents[K]): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off<K extends keyof WebRTCEvents>(event: K, callback: WebRTCEvents[K]): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit<K extends keyof WebRTCEvents>(event: K, ...args: Parameters<WebRTCEvents[K]>): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          (callback as any)(...args);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  disconnect(): void {
    // إيقاف مراقبة الصوت
    if (this.speakingCheckInterval) {
      clearInterval(this.speakingCheckInterval);
      this.speakingCheckInterval = null;
    }

    // مغادرة الغرفة
    this.leaveRoom();

    // إغلاق Socket.IO
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    // إيقاف البث المحلي
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // إغلاق AudioContext
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // مسح البيانات
    this.peers.clear();
    this.audioElements.clear();
    this.listeners.clear();
    this.isInitialized = false;
    this.currentRoomId = null;
    this.currentUserId = null;

    console.log('🔌 WebRTC Service disconnected');
  }

  // Getters
  get isConnected(): boolean {
    return this.isInitialized && this.socket?.connected === true;
  }

  get isMicMuted(): boolean {
    return this.isMuted;
  }

  get roomId(): string | null {
    return this.currentRoomId;
  }

  get connectedPeers(): string[] {
    return Array.from(this.peers.keys());
  }
}

// إنشاء مثيل واحد للخدمة
export const webRTCService = new WebRTCService();
export default webRTCService;
