<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التكامل - الغرف الصوتية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-section {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(34, 197, 94, 0.3); border: 1px solid #22c55e; }
        .error { background: rgba(239, 68, 68, 0.3); border: 1px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.3); border: 1px solid #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.3); border: 1px solid #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background 0.3s;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #6b7280; cursor: not-allowed; }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .user-simulation {
            border: 2px solid #3b82f6;
            margin: 10px 0;
        }
        .user1 { border-color: #22c55e; }
        .user2 { border-color: #f59e0b; }
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
        }
        .status-dot.connected { background: #22c55e; }
        .status-dot.connecting { background: #f59e0b; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 اختبار التكامل - نظام الغرف الصوتية</h1>
        
        <div class="test-section">
            <h2>📊 حالة النظام العامة</h2>
            <div class="connection-status">
                <div id="server-dot" class="status-dot"></div>
                <span>حالة الخادم</span>
            </div>
            <div class="connection-status">
                <div id="socket-dot" class="status-dot"></div>
                <span>Socket.IO</span>
            </div>
            <div class="connection-status">
                <div id="webrtc-dot" class="status-dot"></div>
                <span>WebRTC</span>
            </div>
            <button onclick="checkSystemStatus()">فحص حالة النظام</button>
        </div>

        <div class="test-grid">
            <div class="test-section user-simulation user1">
                <h3>👤 محاكاة المستخدم الأول</h3>
                <div id="user1-status"></div>
                <button onclick="simulateUser1()">بدء المحاكاة</button>
                <button onclick="user1JoinVoice()" disabled id="user1-join">انضمام للصوت</button>
                <button onclick="user1ToggleMic()" disabled id="user1-mic">تبديل المايك</button>
            </div>

            <div class="test-section user-simulation user2">
                <h3>👤 محاكاة المستخدم الثاني</h3>
                <div id="user2-status"></div>
                <button onclick="simulateUser2()">بدء المحاكاة</button>
                <button onclick="user2JoinVoice()" disabled id="user2-join">انضمام للصوت</button>
                <button onclick="user2ToggleMic()" disabled id="user2-mic">تبديل المايك</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 اختبارات التكامل</h2>
            <button onclick="testFullIntegration()">اختبار التكامل الكامل</button>
            <button onclick="testVoiceConnection()">اختبار الاتصال الصوتي</button>
            <button onclick="testRoomSync()">اختبار مزامنة الغرف</button>
            <div id="integration-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 سجل الأحداث</h2>
            <div id="event-log" class="log"></div>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket1, socket2;
        let user1Connected = false, user2Connected = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const color = getLogColor(type);
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getLogColor(type) {
            switch(type) {
                case 'success': return '#22c55e';
                case 'error': return '#ef4444';
                case 'warning': return '#f59e0b';
                default: return '#ffffff';
            }
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        function updateDot(dotId, status) {
            const dot = document.getElementById(dotId);
            dot.className = `status-dot ${status}`;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // فحص حالة النظام
        async function checkSystemStatus() {
            log('بدء فحص حالة النظام...', 'info');
            
            // فحص الخادم
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    updateDot('server-dot', 'connected');
                    log('✅ الخادم يعمل بشكل طبيعي', 'success');
                } else {
                    updateDot('server-dot', 'error');
                    log('❌ مشكلة في الخادم', 'error');
                }
            } catch (error) {
                updateDot('server-dot', 'error');
                log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
            }

            // فحص Socket.IO
            const testSocket = io();
            testSocket.on('connect', () => {
                updateDot('socket-dot', 'connected');
                log('✅ Socket.IO يعمل بشكل طبيعي', 'success');
                testSocket.disconnect();
            });

            testSocket.on('connect_error', () => {
                updateDot('socket-dot', 'error');
                log('❌ مشكلة في Socket.IO', 'error');
            });

            // فحص WebRTC
            try {
                const pc = new RTCPeerConnection();
                await pc.createOffer();
                updateDot('webrtc-dot', 'connected');
                log('✅ WebRTC مدعوم ويعمل', 'success');
                pc.close();
            } catch (error) {
                updateDot('webrtc-dot', 'error');
                log(`❌ مشكلة في WebRTC: ${error.message}`, 'error');
            }
        }

        // محاكاة المستخدم الأول
        function simulateUser1() {
            log('بدء محاكاة المستخدم الأول...', 'info');
            
            socket1 = io();
            
            socket1.on('connect', () => {
                user1Connected = true;
                updateStatus('user1-status', '✅ متصل', 'success');
                document.getElementById('user1-join').disabled = false;
                log('المستخدم الأول: متصل بـ Socket.IO', 'success');
            });

            socket1.on('disconnect', () => {
                user1Connected = false;
                updateStatus('user1-status', '❌ منقطع', 'error');
                document.getElementById('user1-join').disabled = true;
                document.getElementById('user1-mic').disabled = true;
                log('المستخدم الأول: انقطع الاتصال', 'warning');
            });

            socket1.on('user-joined', (data) => {
                log(`المستخدم الأول: مستخدم انضم للغرفة - ${data.user.name}`, 'info');
            });

            socket1.on('user-left', (data) => {
                log(`المستخدم الأول: مستخدم غادر الغرفة - ${data.userId}`, 'info');
            });
        }

        // محاكاة المستخدم الثاني
        function simulateUser2() {
            log('بدء محاكاة المستخدم الثاني...', 'info');
            
            socket2 = io();
            
            socket2.on('connect', () => {
                user2Connected = true;
                updateStatus('user2-status', '✅ متصل', 'success');
                document.getElementById('user2-join').disabled = false;
                log('المستخدم الثاني: متصل بـ Socket.IO', 'success');
            });

            socket2.on('disconnect', () => {
                user2Connected = false;
                updateStatus('user2-status', '❌ منقطع', 'error');
                document.getElementById('user2-join').disabled = true;
                document.getElementById('user2-mic').disabled = true;
                log('المستخدم الثاني: انقطع الاتصال', 'warning');
            });

            socket2.on('user-joined', (data) => {
                log(`المستخدم الثاني: مستخدم انضم للغرفة - ${data.user.name}`, 'info');
            });

            socket2.on('user-left', (data) => {
                log(`المستخدم الثاني: مستخدم غادر الغرفة - ${data.userId}`, 'info');
            });
        }

        // انضمام المستخدم الأول للصوت
        function user1JoinVoice() {
            if (!socket1 || !user1Connected) return;
            
            log('المستخدم الأول: محاولة الانضمام للصوت...', 'info');
            
            socket1.emit('join-voice-room', {
                roomId: 'test-room-123',
                userId: 'user1-test'
            });
            
            document.getElementById('user1-mic').disabled = false;
            log('المستخدم الأول: تم إرسال طلب الانضمام للصوت', 'success');
        }

        // انضمام المستخدم الثاني للصوت
        function user2JoinVoice() {
            if (!socket2 || !user2Connected) return;
            
            log('المستخدم الثاني: محاولة الانضمام للصوت...', 'info');
            
            socket2.emit('join-voice-room', {
                roomId: 'test-room-123',
                userId: 'user2-test'
            });
            
            document.getElementById('user2-mic').disabled = false;
            log('المستخدم الثاني: تم إرسال طلب الانضمام للصوت', 'success');
        }

        // تبديل مايك المستخدم الأول
        function user1ToggleMic() {
            if (!socket1) return;
            
            socket1.emit('toggle-microphone', {
                roomId: 'test-room-123',
                userId: 'user1-test',
                isMuted: Math.random() > 0.5
            });
            
            log('المستخدم الأول: تم تبديل حالة المايكروفون', 'info');
        }

        // تبديل مايك المستخدم الثاني
        function user2ToggleMic() {
            if (!socket2) return;
            
            socket2.emit('toggle-microphone', {
                roomId: 'test-room-123',
                userId: 'user2-test',
                isMuted: Math.random() > 0.5
            });
            
            log('المستخدم الثاني: تم تبديل حالة المايكروفون', 'info');
        }

        // اختبار التكامل الكامل
        async function testFullIntegration() {
            log('🚀 بدء اختبار التكامل الكامل...', 'info');
            
            const results = [];
            
            // 1. فحص النظام
            await checkSystemStatus();
            results.push('✅ فحص النظام مكتمل');
            
            // 2. محاكاة المستخدمين
            if (!user1Connected) simulateUser1();
            if (!user2Connected) simulateUser2();
            
            // انتظار الاتصال
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (user1Connected && user2Connected) {
                results.push('✅ اتصال المستخدمين ناجح');
                
                // 3. اختبار الانضمام للصوت
                user1JoinVoice();
                user2JoinVoice();
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                results.push('✅ اختبار الانضمام للصوت مكتمل');
                
                // 4. اختبار تبديل المايك
                user1ToggleMic();
                user2ToggleMic();
                
                results.push('✅ اختبار تبديل المايكروفون مكتمل');
            } else {
                results.push('❌ فشل في اتصال المستخدمين');
            }
            
            updateStatus('integration-results', results.join('<br>'), 'info');
            log('🎉 اختبار التكامل الكامل مكتمل', 'success');
        }

        // بدء الفحص عند تحميل الصفحة
        window.onload = function() {
            log('🔄 بدء اختبارات التكامل...', 'info');
            checkSystemStatus();
        };
    </script>
</body>
</html>
