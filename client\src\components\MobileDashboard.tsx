import React, { useState, useEffect } from 'react';
import {
  Gamepad2,
  <PERSON>,
  Trophy,
  Settings,
  LogOut,
  Coins,
  Star,
  Crown,
  Box,
  Infinity,
  Menu,
  X,
  Shield,
  Home,
  User,
  ArrowLeft,
  ChevronLeft,
  Mic
} from 'lucide-react';
import GameGrid from './GameGrid';
import AdminDashboard from './AdminDashboard';
import MobileProfileCard from './MobileProfileCard';
import SimpleVoiceRooms from './SimpleVoiceRooms';

interface MobileDashboardProps {
  userData: any;
  onLogout: () => void;
  onUpdateProfile?: (data: any) => void;
}

const MobileDashboard: React.FC<MobileDashboardProps> = ({ userData, onLogout, onUpdateProfile }) => {
  const [activeTab, setActiveTab] = useState<'games' | 'leaderboard' | 'profile' | 'admin' | 'voice-rooms'>('games');
  const [currentUserData, setCurrentUserData] = useState(userData);
  const [navigationHistory, setNavigationHistory] = useState<('games' | 'leaderboard' | 'profile' | 'admin' | 'voice-rooms')[]>(['games']);

  // دالة تحديث الملف الشخصي
  const handleProfileUpdate = (updatedData: any) => {
    console.log('🔄 MobileDashboard: Updating profile data:', updatedData);
    const newUserData = { ...currentUserData, ...updatedData };
    setCurrentUserData(newUserData);

    if (onUpdateProfile) {
      onUpdateProfile(newUserData);
    }
  };

  // تحديث بيانات المستخدم
  useEffect(() => {
    setCurrentUserData(userData);
  }, [userData]);

  // دالة التنقل مع تتبع التاريخ
  const navigateToTab = (tab: 'games' | 'leaderboard' | 'profile' | 'admin' | 'voice-rooms') => {
    console.log('🔄 Navigating to tab:', tab);
    if (tab !== activeTab) {
      // استخدام requestAnimationFrame لتحسين الأداء
      requestAnimationFrame(() => {
        setNavigationHistory(prev => [...prev, tab]);
        setActiveTab(tab);
        console.log('✅ Tab changed to:', tab);
      });
    }
  };

  // دالة الرجوع للقائمة السابقة
  const goBack = () => {
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // إزالة الصفحة الحالية
      const previousTab = newHistory[newHistory.length - 1];
      setNavigationHistory(newHistory);
      setActiveTab(previousTab);
    }
  };

  // التحقق من إمكانية الرجوع
  const canGoBack = navigationHistory.length > 1;

  // الحصول على عنوان القسم الحالي
  const getCurrentTabTitle = () => {
    switch (activeTab) {
      case 'games':
        return 'مركز الألعاب';
      case 'voice-rooms':
        return 'قاعة الغرف الصوتية';
      case 'leaderboard':
        return 'لوحة المتصدرين';
      case 'profile':
        return 'الملف الشخصي';
      case 'admin':
        return 'لوحة الإدارة';
      default:
        return 'INFINITY BOX';
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'games':
        return <GameGrid setActiveTab={navigateToTab} />;
      case 'voice-rooms':
        return <SimpleVoiceRooms userData={currentUserData} onBack={() => setActiveTab('games')} />;
      case 'leaderboard':
        return <LeaderboardContent />;
      case 'profile':
        return <MobileProfileCard
          userData={currentUserData}
          onUpdateProfile={handleProfileUpdate}
          onLogout={onLogout}
          onNavigateToVoiceRooms={() => setActiveTab('voice-rooms')}
          isOwner={true}
        />;
      case 'admin':
        return userData?.isAdmin ? <AdminDashboard userData={userData} onLogout={onLogout} /> : <GameGrid setActiveTab={navigateToTab} />;
      default:
        return <GameGrid setActiveTab={navigateToTab} />;
    }
  };

  // إذا كان المستخدم في لوحة المشرف، عرضها بدون الشريط الجانبي
  if (activeTab === 'admin' && userData?.isAdmin) {
    return <AdminDashboard userData={userData} onLogout={onLogout} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white flex flex-col relative overflow-hidden">
      {/* خلفية متحركة مطابقة */}
      <div className="absolute inset-0 overflow-hidden">
        {/* نجوم متحركة في السماء الليلية */}
        <div className="absolute inset-0 opacity-20">
          <div className="grid grid-cols-16 gap-6 h-full w-full p-4">
            {Array.from({ length: 80 }).map((_, i) => (
              <div
                key={i}
                className={`${i % 4 === 0 ? 'w-1 h-1' : 'w-0.5 h-0.5'} bg-white rounded-full animate-pulse`}
                style={{
                  animationDelay: `${i * 150}ms`,
                  animationDuration: `${2 + (i % 3)}s`
                }}
              ></div>
            ))}
          </div>
        </div>

        {/* قمر في الخلفية */}
        <div className="absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"></div>

        {/* تأثير خلفي متحرك - رمز اللانهاية */}
        <div className="absolute inset-0 flex items-center justify-center opacity-5">
          <div className="text-8xl font-black text-white animate-spin" style={{ animationDuration: '25s' }}>∞</div>
        </div>
      </div>

      {/* Header للهاتف */}
      <div className="relative z-10 flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-900/95 to-slate-800/95 backdrop-blur-sm border-b border-blue-400/30 sticky top-0 z-40">
        <div className="flex items-center gap-3">
          {/* زر الرجوع */}
          {canGoBack && (
            <button
              onClick={goBack}
              className="p-2 rounded-lg hover:bg-slate-700/50 transition-colors"
            >
              <ChevronLeft className="w-5 h-5 text-white" />
            </button>
          )}

          <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 via-amber-500 to-blue-600 rounded-xl flex items-center justify-center relative">
            <Box className="w-4 h-4 text-white" />
            <Infinity className="w-2 h-2 text-white absolute -top-0.5 -right-0.5" />
          </div>
          <div>
            <h1 className="text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent">
              {activeTab === 'games' ? 'INFINITY BOX' : getCurrentTabTitle()}
            </h1>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1">
            <Coins className="w-4 h-4 text-yellow-400" />
            <span className="text-yellow-400 text-sm font-bold">{currentUserData?.goldCoins || 0}</span>
          </div>
          <div className="flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1">
            <Star className="w-4 h-4 text-blue-400" />
            <span className="text-blue-400 text-sm font-bold">{currentUserData?.pearls || 0}</span>
          </div>

          {/* زر الغرفة الصوتية */}
          <button
            onClick={() => setActiveTab('voice-rooms')}
            className="group relative p-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg border border-purple-500/30 hover:border-purple-500/50 transition-all duration-300 hover:scale-105"
            title="غرفتي الصوتية"
          >
            <Mic className="w-4 h-4 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />

            {/* تأثير التوهج */}
            <div className="absolute inset-0 bg-purple-500/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="relative z-10 flex-1 overflow-hidden">
        {renderContent()}
      </div>

      {/* Bottom Navigation للهاتف */}
      <div className="relative z-10 bg-gradient-to-r from-blue-900/95 to-slate-800/95 backdrop-blur-sm border-t border-blue-400/30 px-2 py-2 sticky bottom-0 z-40">
        <div className="flex items-center justify-around">
          <button
            onClick={() => navigateToTab('games')}
            className={`flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200 ${
              activeTab === 'games'
                ? 'bg-blue-600/30 text-blue-400'
                : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
            }`}
          >
            <Gamepad2 className="w-5 h-5" />
            <span className="text-xs font-medium">الألعاب</span>
          </button>



          <button
            onClick={() => navigateToTab('leaderboard')}
            className={`flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200 ${
              activeTab === 'leaderboard'
                ? 'bg-yellow-600/30 text-yellow-400'
                : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
            }`}
          >
            <Trophy className="w-5 h-5" />
            <span className="text-xs font-medium">المتصدرين</span>
          </button>

          <button
            onClick={() => navigateToTab('profile')}
            className={`flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200 ${
              activeTab === 'profile'
                ? 'bg-green-600/30 text-green-400'
                : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
            }`}
          >
            <User className="w-5 h-5" />
            <span className="text-xs font-medium">الملف الشخصي</span>
          </button>

          {userData?.isAdmin && (
            <button
              onClick={() => navigateToTab('admin')}
              className={`flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200 ${
                activeTab === 'admin'
                  ? 'bg-red-600/30 text-red-400'
                  : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
              }`}
            >
              <Crown className="w-5 h-5" />
              <span className="text-xs font-medium">الإدارة</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// مكون لوحة المتصدرين
const LeaderboardContent: React.FC = () => {
  return (
    <div className="p-4 h-full overflow-y-auto">
      <div className="text-center py-20">
        <Trophy className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">لوحة المتصدرين</h2>
        <p className="text-gray-400">قريباً...</p>
      </div>
    </div>
  );
};

export default MobileDashboard;
