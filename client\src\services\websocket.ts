import { Message, VoiceUser } from '../types';

export type WebSocketEventType =
  | 'voice_room_users'
  | 'player_joined'
  | 'player_left'
  | 'chat_message'
  | 'user_speaking'
  | 'user_muted'
  | 'room_created'
  | 'room_deleted'
  | 'user_auth'
  | 'auth_success'
  | 'voice_status_update'
  | 'get_room_users'
  | 'connection_established'
  | 'private_message'
  | 'new_message'
  | 'join_voice_room'
  | 'leave_voice_room'
  | 'voice_room_message'
  | 'voice_room_update';

export interface WebSocketMessage {
  type: WebSocketEventType;
  data?: any;
  roomId?: string;
  username?: string;
  userId?: string;
  users?: string[];
  sender?: string;
  text?: string;
  message?: string;
  isAdmin?: boolean;
  isMuted?: boolean;
  isSpeaking?: boolean;
  timestamp?: string;
}

class WebSocketService {
  public ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<WebSocketEventType, Set<Function>> = new Map();
  private isConnecting = false;

  constructor(private url: string = `ws${window.location.protocol === 'https:' ? 's' : ''}://${window.location.host}/ws`) {}

  connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return Promise.resolve();
    }

    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        console.log('Connecting to WebSocket:', this.url);
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected successfully');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket closed:', event.code, event.reason);
          this.isConnecting = false;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          if (this.reconnectAttempts === 0) {
            reject(error);
          }
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private handleMessage(message: WebSocketMessage) {
    console.log('📨 WebSocket message received:', message.type, message);
    const listeners = this.listeners.get(message.type);
    if (listeners) {
      console.log(`🎯 Found ${listeners.size} listeners for ${message.type}`);
      listeners.forEach(callback => {
        try {
          callback(message);
        } catch (error) {
          console.error('Error in WebSocket message handler:', error);
        }
      });
    } else {
      console.log(`❌ No listeners found for message type: ${message.type}`);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  send(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  on(eventType: WebSocketEventType, callback: Function) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(callback);
  }

  off(eventType: WebSocketEventType, callback: Function) {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // إرسال رسالة خاصة
  sendPrivateMessage(recipientId: string, content: string, messageData: any) {
    if (this.isConnected) {
      this.send({
        type: 'private_message',
        data: {
          recipientId,
          content,
          messageData
        }
      });
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

export const wsService = new WebSocketService();